import React, { useState } from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Bar } from 'react-chartjs-2';
import { Button, Box, Alert, AlertTitle } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import WarningIcon from '@mui/icons-material/Warning';
import CollectionStatusDetailModal from './CollectionStatusDetailModal';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 债权清收情况图表组件（支持存量债权和新增债权）
 * @param {Object} props
 * @param {Object} props.data - 数据对象，包含 yearBeginAmount, monthDisposalAmount, yearCumulativeAmount, periodEndAmount
 * @param {String} props.title - 图表标题
 * @returns {JSX.Element}
 */
const CollectionStatusChart = ({ data, title = '存量债权清收情况' }) => {
  const [detailModalOpen, setDetailModalOpen] = useState(false);

  // 数据验证和默认值处理
  const chartData = {
    yearBeginAmount: data?.yearBeginAmount || 0,
    monthCollectionAmount: data?.monthCollectionAmount || 0, // 更新字段名
    yearCumulativeCollectionAmount: data?.yearCumulativeCollectionAmount || 0, // 更新字段名
    periodEndAmount: data?.periodEndAmount || 0
  };

  // 🔍 数据一致性验证
  // 对于新增债权，期初金额为0，期末余额 = 新增金额 - 累计处置金额
  // 对于存量债权，期末余额 = 期初金额 - 累计处置金额
  const isNewDebt = chartData.yearBeginAmount === 0; // 判断是否为新增债权

  let calculatedEndAmount;
  if (isNewDebt) {
    // 新增债权情况：期末余额应该等于期初金额（0）+ 新增金额 - 累计处置金额
    // 但由于我们没有单独的新增金额字段，我们用另一种方式计算
    calculatedEndAmount =
      chartData.yearBeginAmount +
      (chartData.periodEndAmount + chartData.yearCumulativeCollectionAmount) -
      chartData.yearCumulativeCollectionAmount;
    // 简化为：calculatedEndAmount = chartData.periodEndAmount（对新增债权，不做一致性校验或使用不同的校验逻辑）
  } else {
    // 存量债权情况：期末余额 = 期初金额 - 累计处置金额
    calculatedEndAmount = chartData.yearBeginAmount - chartData.yearCumulativeCollectionAmount;
  }

  const difference = isNewDebt ? 0 : chartData.periodEndAmount - calculatedEndAmount; // 新增债权跳过一致性检查
  const hasDataInconsistency = !isNewDebt && Math.abs(difference) > 0.01; // 差异超过0.01万元认为不一致

  // 如果存在数据差异，在控制台输出警告
  if (hasDataInconsistency) {
    const debtType = isNewDebt ? '新增债权' : '存量债权';
    console.warn(`⚠️ ${debtType}清收数据不一致:`, {
      期初金额: chartData.yearBeginAmount,
      本年累计处置: chartData.yearCumulativeCollectionAmount,
      理论期末余额: calculatedEndAmount,
      实际期末余额: chartData.periodEndAmount,
      差异: difference,
      债权类型: debtType
    });
  }

  // 图表标签
  const labels = ['期初金额', '本月清收处置', '本年累计处置', '期末余额']; // 更新标签

  // 根据债权类型选择配色方案
  const getColorScheme = () => {
    if (isNewDebt) {
      // 新增债权配色方案：绿色系
      return {
        backgroundColor: [
          'rgba(39, 174, 96, 0.3)', // 浅绿色 - 期初金额（新增债权为0，用浅色表示）
          'rgba(46, 134, 193, 0.8)', // 蓝色系 - 本月处置金额
          'rgba(46, 134, 193, 0.9)', // 深蓝色 - 本年累计处置
          'rgba(231, 76, 60, 0.8)' // 红色系 - 期末余额
        ],
        borderColor: [
          'rgba(39, 174, 96, 0.5)', // 浅绿色边框 - 期初金额
          'rgba(46, 134, 193, 1)', // 蓝色边框 - 本月处置金额
          'rgba(46, 134, 193, 1)', // 深蓝色边框 - 本年累计处置
          'rgba(231, 76, 60, 1)' // 红色边框 - 期末余额
        ]
      };
    } else {
      // 存量债权配色方案：原有的红绿配色
      return {
        backgroundColor: [
          'rgba(244, 67, 54, 0.8)', // 红色 - 期初金额
          'rgba(76, 175, 80, 0.8)', // 标准绿色 - 本月清收处置
          'rgba(129, 199, 132, 0.8)', // 浅绿色 - 本年累计处置
          'rgba(244, 67, 54, 0.8)' // 红色 - 期末余额
        ],
        borderColor: [
          'rgba(244, 67, 54, 1)', // 红色 - 期初金额
          'rgba(76, 175, 80, 1)', // 标准绿色 - 本月清收处置
          'rgba(129, 199, 132, 1)', // 浅绿色 - 本年累计处置
          'rgba(244, 67, 54, 1)' // 红色 - 期末余额
        ]
      };
    }
  };

  const colorScheme = getColorScheme();

  // 图表数据配置
  const barChartData = {
    labels,
    datasets: [
      {
        label: '金额',
        data: [
          Number(chartData.yearBeginAmount) || 0,
          Number(chartData.monthCollectionAmount) || 0,
          Number(chartData.yearCumulativeCollectionAmount) || 0,
          Number(chartData.periodEndAmount) || 0
        ],
        backgroundColor: colorScheme.backgroundColor,
        borderColor: colorScheme.borderColor,
        borderWidth: 1,
        barThickness: 40
      }
    ]
  };

  // 图表选项配置
  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false // 隐藏图例，因为只有一个数据集
      },
      title: {
        display: true,
        text: title,
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      tooltip: {
        callbacks: {
          label: ctx => `${ctx.label}: ${(ctx.parsed?.y || 0).toFixed(2)} 万元`
        }
      },
      datalabels: {
        display: ctx => ctx.dataset.data[ctx.dataIndex] > 0,
        formatter: value => (value > 0 ? Math.round(value) : ''),
        anchor: 'end',
        align: 'start',
        offset: -5,
        color: '#000',
        font: { size: 11, weight: 'bold' }
      }
    },
    scales: {
      y: {
        type: 'linear',
        title: {
          display: false // 不显示Y轴标题
        },
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            // 确保值存在且为数字
            if (value === null || value === undefined || isNaN(value)) {
              return '0';
            }
            return value.toFixed(0);
          }
        }
      },
      x: {
        grid: {
          display: false // 移除X轴网格线
        },
        ticks: {
          font: {
            size: 12
          }
        }
      }
    },
    maintainAspectRatio: false
  };

  // 处理更多信息按钮点击事件
  const handleMoreInfo = () => {
    setDetailModalOpen(true);
  };

  return (
    <Box
      className="p-6 bg-white rounded-lg shadow-md"
      sx={{
        position: 'relative',
        width: '100%',
        maxWidth: '100%',
        marginBottom: '20px',
        overflow: 'hidden' // 防止内容溢出
      }}
    >
      {/* 更多信息按钮 */}
      <Button
        variant="text"
        size="small"
        startIcon={<InfoOutlinedIcon />}
        onClick={handleMoreInfo}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          zIndex: 1,
          color: 'primary.main',
          '&:hover': {
            backgroundColor: 'rgba(76, 175, 80, 0.08)'
          }
        }}
      >
        详细信息
      </Button>

      {/* 数据一致性警告 */}
      {hasDataInconsistency && (
        <Alert
          severity="warning"
          icon={<WarningIcon />}
          sx={{
            mb: 2,
            mt: 1,
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            borderColor: 'rgba(255, 193, 7, 0.3)'
          }}
        >
          <AlertTitle>数据一致性提醒</AlertTitle>
          检测到数据差异 {difference.toFixed(2)}{' '}
          万元。期初金额减累计处置应等于期末余额，请核实数据准确性。
        </Alert>
      )}

      {/* 图表容器 */}
      <Box
        sx={{
          height: '350px',
          width: '100%',
          pt: hasDataInconsistency ? 1 : 2 // 根据是否有警告调整间距
        }}
      >
        <Bar data={barChartData} options={options} style={{ height: '100%', width: '100%' }} />
      </Box>

      {/* 数据汇总信息 */}
      <Box
        sx={{
          mt: 2,
          pt: 2,
          borderTop: '1px solid #e0e0e0',
          display: 'flex',
          justifyContent: 'space-around',
          flexWrap: 'wrap',
          gap: 1,
          width: '100%',
          overflow: 'hidden' // 防止溢出
        }}
      >
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '80px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            期初金额
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: isNewDebt ? 'rgba(39, 174, 96, 0.7)' : 'rgba(244, 67, 54, 1)',
              whiteSpace: 'nowrap'
            }}
          >
            {chartData.yearBeginAmount.toFixed(2)} 万元
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '90px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            本月清收处置
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: isNewDebt ? 'rgba(46, 134, 193, 1)' : 'rgba(76, 175, 80, 1)',
              whiteSpace: 'nowrap'
            }}
          >
            {chartData.monthCollectionAmount.toFixed(2)} 万元
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '90px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            本年累计处置
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: isNewDebt ? 'rgba(46, 134, 193, 1)' : 'rgba(129, 199, 132, 1)',
              whiteSpace: 'nowrap'
            }}
          >
            {chartData.yearCumulativeCollectionAmount.toFixed(2)} 万元
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '80px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            期末余额
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: isNewDebt ? 'rgba(231, 76, 60, 1)' : 'rgba(244, 67, 54, 1)',
              whiteSpace: 'nowrap'
            }}
          >
            {chartData.periodEndAmount.toFixed(2)} 万元
          </Box>
        </Box>
      </Box>

      {/* 明细弹窗 */}
      <CollectionStatusDetailModal
        open={detailModalOpen}
        onClose={() => setDetailModalOpen(false)}
        data={data}
      />
    </Box>
  );
};

CollectionStatusChart.propTypes = {
  data: PropTypes.shape({
    yearBeginAmount: PropTypes.number,
    monthCollectionAmount: PropTypes.number,
    yearCumulativeCollectionAmount: PropTypes.number,
    periodEndAmount: PropTypes.number,
    yearBeginDetails: PropTypes.array,
    monthCollectionDetails: PropTypes.array,
    yearCumulativeDetails: PropTypes.array,
    periodEndDetails: PropTypes.array
  }),
  title: PropTypes.string
};

CollectionStatusChart.defaultProps = {
  data: {
    yearBeginAmount: 0,
    monthCollectionAmount: 0,
    yearCumulativeCollectionAmount: 0,
    periodEndAmount: 0,
    yearBeginDetails: [],
    monthCollectionDetails: [],
    yearCumulativeDetails: [],
    periodEndDetails: []
  },
  title: '债权清收情况' // 支持存量债权和新增债权
};

export default CollectionStatusChart;
