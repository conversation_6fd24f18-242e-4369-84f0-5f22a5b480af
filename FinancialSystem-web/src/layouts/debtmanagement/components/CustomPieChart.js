import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Legend, Toolt<PERSON>, ResponsiveContainer } from 'recharts';
import { Card, CardContent } from './ui/card';
import PropTypes from 'prop-types';

// 自定义标签渲染函数
const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }) => {
  // 安全检查所有必需的参数
  if (!cx || !cy || midAngle === undefined || !outerRadius || percent === undefined) {
    return null;
  }

  const RADIAN = Math.PI / 180;
  const radius = outerRadius * 0.6;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  // 确保百分比是有效数字
  const safePercent = isNaN(percent) ? 0 : percent;

  return (
    <text
      x={x}
      y={y}
      fill="black"
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      fontSize="16"
      fontWeight="lighter"
    >
      {`${(safePercent * 100).toFixed(1)}%`}
    </text>
  );
};

/** 自定义 Tooltip 组件 */
const CustomTooltip = ({ active, payload, coordinate }) => {
  // 安全检查所有必需的参数
  if (!active || !payload?.[0] || !coordinate) {
    return null;
  }

  const { x, y } = coordinate;
  const data = payload[0];

  // 确保坐标值有效
  if (x === undefined || y === undefined || isNaN(x) || isNaN(y)) {
    return null;
  }

  return (
    <div
      style={{
        position: 'absolute',
        top: `${y}px`,
        left: `${x}px`,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        padding: '5px 10px',
        borderRadius: '4px',
        color: data.fill || '#000',
        fontSize: '12px',
        fontWeight: 'lighter',
        transform: 'translate(-50%, -50%)',
        boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
        border: '1px solid #f0f0f0',
        minWidth: '75px',
        minHeight: '40px'
      }}
    >
      <span style={{ display: 'block' }}>
        {data.name || '未知'}: {data.value || 0}
      </span>
    </div>
  );
};

const CustomPieChart = ({ title, data, summaryText }) => {
  // 安全处理数据，确保数据格式正确
  const safeData = Array.isArray(data)
    ? data
        .filter(item => {
          // 过滤掉无效数据
          return (
            item &&
            typeof item === 'object' &&
            item.name &&
            item.value !== undefined &&
            item.value !== null &&
            !isNaN(Number(item.value))
          );
        })
        .map(item => ({
          ...item,
          value: Number(item.value) || 0, // 确保value是数字
          name: String(item.name || '未知'), // 确保name是字符串
          color: item.color || '#8884d8' // 提供默认颜色
        }))
    : [];

  // 如果没有有效数据，显示空状态
  if (safeData.length === 0) {
    return (
      <Card
        className="p-4 shadow-lg rounded-lg"
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <div className="text-center">
          <h2 className="text-sm font-semibold mb-4" style={{ fontSize: '17px' }}>
            {title}
          </h2>
          <p className="text-gray-500">暂无数据</p>
        </div>
      </Card>
    );
  }

  return (
    <Card
      className="p-4 shadow-lg rounded-lg"
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* 标题部分 - 左上角 */}
      <div className="flex justify-center items-center mb-2" style={{ width: '100%' }}>
        <h2 className="text-sm font-semibold" style={{ fontSize: '17px' }}>
          {title}
        </h2>
      </div>

      {/* 饼图部分 - 中间显示 */}
      <CardContent className="flex-grow flex justify-center items-center p-0">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
            <Pie
              data={safeData}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="45%"
              outerRadius="85%"
              innerRadius="0%"
              paddingAngle={0}
              labelLine={false}
              label={renderCustomLabel}
            >
              {safeData.map((entry, index) => (
                <Cell key={`cell-${entry.name}-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend
              verticalAlign="bottom"
              align="center"
              iconType="circle"
              layout="horizontal"
              wrapperStyle={{
                fontSize: '12px',
                paddingTop: '5px',
                paddingBottom: '0px',
                fontWeight: 'bold'
              }}
            />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>

      {/* 底部信息区域 - 只显示摘要文本 */}
      {summaryText && (
        <div style={{ marginTop: 'auto' }}>
          <p
            className="text-gray-700"
            style={{
              fontSize: '12px'
            }}
          >
            {summaryText}
          </p>
        </div>
      )}
    </Card>
  );
};

// 添加 PropTypes 校验
CustomTooltip.propTypes = {
  active: PropTypes.bool,
  payload: PropTypes.arrayOf(
    PropTypes.shape({
      fill: PropTypes.string,
      name: PropTypes.string,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    })
  ),
  coordinate: PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired
  }).isRequired
};

// 🔹 添加 PropTypes 校验
CustomPieChart.propTypes = {
  title: PropTypes.string.isRequired, // title 是必传的
  data: PropTypes.array.isRequired, // data 是必传的
  summaryText: PropTypes.string // summaryText 是可选的
};

// 如果 summaryText 没有传递，使用空字符串作为默认值
CustomPieChart.defaultProps = {
  summaryText: '' // 默认值为空字符串
};

export default CustomPieChart;
