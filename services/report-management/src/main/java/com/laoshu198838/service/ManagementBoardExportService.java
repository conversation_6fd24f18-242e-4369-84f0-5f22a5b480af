package com.laoshu198838.service;

import com.aspose.cells.*;
import com.laoshu198838.dto.report.OperationalDashboardDTO;
import com.laoshu198838.repository.overdue_debt.DebtDetailsExportRepository;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDecreaseRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDetailRepository;
import com.laoshu198838.util.business.DebtDisposalSplitter;
import com.laoshu198838.util.business.Top80PercentFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 经营调度会看板导出服务
 * 实现经营调度会看板的完整业务逻辑，包括：
 * 1. 期初存量债权和本年累计新增的计算
 * 2. 处置金额拆分算法（新增/存量）
 * 3. 前80%筛选算法
 * 4. Excel精确位置填充功能
 * 
 * <AUTHOR> Code (Enhanced)
 * @since 2025-01-18
 */
@Service
public class ManagementBoardExportService {
    
    private static final Logger logger = LoggerFactory.getLogger(ManagementBoardExportService.class);
    
    // Repository依赖
    @Autowired
    private ImpairmentReserveRepository impairmentReserveRepository;
    
    @Autowired
    private OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;
    
    @Autowired
    private OverdueDebtDetailRepository overdueDebtDetailRepository;
    
    @Autowired
    private DebtDetailsExportRepository debtDetailsExportRepository;
    
    // 业务算法工具
    @Autowired
    private DebtDisposalSplitter debtDisposalSplitter;
    
    @Autowired
    private Top80PercentFilter top80PercentFilter;
    
    // Excel位置映射配置
    private static final Map<String, CellPosition> SUMMARY_POSITIONS = Map.of(
        "期初存量债权", new CellPosition(5, 1),      // B6
        "本年累计新增", new CellPosition(8, 1),      // B9
        "存量债权累计处置", new CellPosition(5, 3),   // D6
        "本月清收金额", new CellPosition(5, 2),      // C6
        "分期还款", new CellPosition(12, 1),         // B13
        "现金处置", new CellPosition(13, 1),         // B14
        "资产抵债其他", new CellPosition(16, 1)      // B17
    );
    
    private static final CellPosition STOCK_COMPANY_START = new CellPosition(21, 1);  // B22
    private static final CellPosition NEW_COMPANY_START = new CellPosition(21, 8);    // I22
    private static final CellPosition STOCK_DETAIL_START = new CellPosition(4, 13);   // N5
    private static final CellPosition NEW_DETAIL_START = new CellPosition(14, 13);    // N15

    /**
     * 导出经营调度会看板数据（统一调用入口）
     * 
     * @param year 年份
     * @param month 月份
     * @param minAmount 最小金额（万元）
     * @return Excel文件的字节数组响应
     */
    public ResponseEntity<byte[]> exportManagementBoard(String year, String month, String minAmount) {
        try {
            logger.info("开始导出经营调度会看板数据 - 年份: {}, 月份: {}, 最小金额: {}万元", year, month, minAmount);
            
            // 1. 并行获取看板所需的所有数据
            ManagementBoardDataBundle dataBundle = getManagementBoardData(year, month);
            
            // 2. 使用模板文件创建Excel工作簿
            Workbook workbook = loadTemplateWorkbook();
            Worksheet worksheet = workbook.getWorksheets().get(0);
            Cells cells = worksheet.getCells();
            
            // 3. 填充汇总数据到Excel固定位置
            fillSummaryData(cells, year, month, dataBundle);
            
            // 4. 填充公司汇总数据
            fillCompanySummaryData(cells, year, month, dataBundle);
            
            // 5. 填充明细数据（经过前80%筛选）
            fillDetailData(cells, year, month, minAmount, dataBundle);
            
            // 6. 导出为字节数组
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.save(byteArrayOutputStream, SaveFormat.XLSX);
            byte[] excelData = byteArrayOutputStream.toByteArray();
            
            // 7. 生成文件名
            String filename = String.format("经营调度会看板_%s年%02d月.xlsx", year, Integer.parseInt(month));
            String encodedFilename = generateSafeFilename(filename);
            
            logger.info("成功导出经营调度会看板数据");
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFilename)
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .header(HttpHeaders.CACHE_CONTROL, "must-revalidate, post-check=0, pre-check=0")
                .header(HttpHeaders.PRAGMA, "public")
                .header(HttpHeaders.EXPIRES, "0")
                .body(excelData);
                
        } catch (Exception e) {
            logger.error("导出经营调度会看板数据失败", e);
            
            String errorMessage = "导出失败: " + e.getMessage();
            return ResponseEntity.internalServerError()
                .contentType(MediaType.TEXT_PLAIN)
                .body(errorMessage.getBytes(StandardCharsets.UTF_8));
        }
    }
    
    /**
     * 批量获取看板所需的所有数据（并行查询优化）
     */
    private ManagementBoardDataBundle getManagementBoardData(String year, String month) {
        ManagementBoardDataBundle bundle = new ManagementBoardDataBundle();
        Integer monthInt = Integer.parseInt(month);
        
        try {
            // 并行执行多个查询以提高性能
            CompletableFuture<BigDecimal> stockDebtFuture = CompletableFuture
                .supplyAsync(() -> impairmentReserveRepository.findStockDebtBeginAmount("2024", "12", "2025年新增债权"));
            
            CompletableFuture<Object[]> disposalMethodsFuture = CompletableFuture
                .supplyAsync(() -> overdueDebtDecreaseRepository.findDisposalMethodsSummaryNative(year, monthInt));
            
            CompletableFuture<List<Object[]>> stockCompanyFuture = CompletableFuture
                .supplyAsync(() -> impairmentReserveRepository.findStockDebtCompanySummary(year, month, "2025年新增债权"));
            
            CompletableFuture<List<Object[]>> newCompanyFuture = CompletableFuture
                .supplyAsync(() -> overdueDebtDetailRepository.findNewDebtCompanySummary(year, monthInt));
            
            CompletableFuture<BigDecimal> yearNewDebtFuture = CompletableFuture
                .supplyAsync(() -> overdueDebtDetailRepository.findYearCumulativeNewDebtAmount(year, monthInt));
            
            // 等待所有查询完成
            CompletableFuture.allOf(stockDebtFuture, disposalMethodsFuture, stockCompanyFuture, newCompanyFuture, yearNewDebtFuture).join();
            
            // 收集结果
            bundle.setStockDebtBegin(stockDebtFuture.get());
            bundle.setDisposalMethods(disposalMethodsFuture.get());
            bundle.setStockCompanySummary(stockCompanyFuture.get());
            bundle.setNewCompanySummary(newCompanyFuture.get());
            bundle.setYearCumulativeNewDebt(yearNewDebtFuture.get());
            
        } catch (Exception e) {
            logger.error("并行查询失败", e);
            throw new RuntimeException("数据查询失败", e);
        }
        
        return bundle;
    }

    /**
     * 填充汇总数据到Excel固定位置
     */
    private void fillSummaryData(Cells cells, String year, String month, ManagementBoardDataBundle dataBundle) {
        try {
            // 1. B6: 期初存量债权（万元）
            BigDecimal stockDebtBegin = dataBundle.getStockDebtBegin();
            setCellValue(cells, SUMMARY_POSITIONS.get("期初存量债权"), 
                        stockDebtBegin.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // 2. B9: 本年累计新增（万元）
            BigDecimal yearCumulativeNew = dataBundle.getYearCumulativeNewDebt();
            setCellValue(cells, SUMMARY_POSITIONS.get("本年累计新增"), 
                        yearCumulativeNew.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // 3. 处置方式统计（B13、B14、B17）
            Object[] disposalMethods = dataBundle.getDisposalMethods();
            if (disposalMethods != null && disposalMethods.length >= 5) {
                // B14: 现金处置
                setCellValue(cells, SUMMARY_POSITIONS.get("现金处置"), 
                            toBigDecimal(disposalMethods[0]).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                
                // B13: 分期还款
                setCellValue(cells, SUMMARY_POSITIONS.get("分期还款"), 
                            toBigDecimal(disposalMethods[1]).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                
                // B17: 资产抵债+其他
                BigDecimal assetAndOther = toBigDecimal(disposalMethods[2]).add(toBigDecimal(disposalMethods[3]));
                setCellValue(cells, SUMMARY_POSITIONS.get("资产抵债其他"), 
                            assetAndOther.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            }
            
            logger.info("汇总数据填充完成");
            
        } catch (Exception e) {
            logger.error("填充汇总数据失败", e);
            throw new RuntimeException("汇总数据填充失败: " + e.getMessage(), e);
        }
    }

    /**
     * 填充公司汇总数据
     */
    private void fillCompanySummaryData(Cells cells, String year, String month, ManagementBoardDataBundle dataBundle) {
        try {
            // 存量债权公司汇总 (B22开始)
            List<Object[]> stockCompanySummary = dataBundle.getStockCompanySummary();
            fillCompanySummaryTable(cells, stockCompanySummary, STOCK_COMPANY_START, 
                               Arrays.asList("公司名称", "期初余额", "累计处置"));
            
            // 新增债权公司汇总 (I22开始)
            List<Object[]> newCompanySummary = dataBundle.getNewCompanySummary();
            fillCompanySummaryTable(cells, newCompanySummary, NEW_COMPANY_START,
                               Arrays.asList("公司名称", "累计新增", "剩余余额"));
            
            logger.info("公司汇总数据填充完成");
            
        } catch (Exception e) {
            logger.error("填充公司汇总数据失败", e);
            throw new RuntimeException("公司汇总数据填充失败: " + e.getMessage(), e);
        }
    }

    /**
     * 填充明细数据（经过前80%筛选）
     */
    private void fillDetailData(Cells cells, String year, String month, String minAmount, ManagementBoardDataBundle dataBundle) {
        try {
            Integer monthInt = Integer.parseInt(month);
            BigDecimal minAmountValue = new BigDecimal(minAmount);
            
            // 1. 获取存量债权明细数据
            List<Object[]> stockDetails = impairmentReserveRepository.findStockDebtDetails(
                year, month, "2025年新增债权", minAmountValue.multiply(new BigDecimal("10000")));
            
            // 2. 应用前80%筛选算法
            List<Object[]> filteredStockDetails = top80PercentFilter.filterTop80Percent(
                stockDetails, 
                row -> toBigDecimal(row[3]), // 本月末债权余额
                minAmountValue.multiply(new BigDecimal("10000"))
            );
            
            // 3. 填充存量债权明细 (N5开始)
            fillDetailTable(cells, filteredStockDetails, STOCK_DETAIL_START, "存量债权");
            
            // 4. 获取新增债权明细数据
            List<Object[]> newDetails = overdueDebtDetailRepository.findNewDebtDetails(
                year, monthInt, minAmountValue.multiply(new BigDecimal("10000")));
            
            // 5. 应用前80%筛选算法
            List<Object[]> filteredNewDetails = top80PercentFilter.filterTop80Percent(
                newDetails,
                row -> toBigDecimal(row[3]), // 累计新增
                minAmountValue.multiply(new BigDecimal("10000"))
            );
            
            // 6. 填充新增债权明细 (N15开始)
            fillDetailTable(cells, filteredNewDetails, NEW_DETAIL_START, "新增债权");
            
            logger.info("明细数据填充完成，存量债权明细: {}条, 新增债权明细: {}条", 
                       filteredStockDetails.size(), filteredNewDetails.size());
            
        } catch (Exception e) {
            logger.error("填充明细数据失败", e);
            throw new RuntimeException("明细数据填充失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 加载Excel模板文件
     */
    private Workbook loadTemplateWorkbook() throws Exception {
        try {
            // 从classpath加载模板文件
            Resource templateResource = new ClassPathResource("templates/经营调度会看板模板.xlsx");
            
            if (!templateResource.exists()) {
                logger.warn("模板文件不存在，使用动态创建的方式");
                return createDefaultWorkbook();
            }
            
            try (InputStream inputStream = templateResource.getInputStream()) {
                Workbook workbook = new Workbook(inputStream);
                logger.info("成功加载Excel模板文件");
                return workbook;
            }
            
        } catch (Exception e) {
            logger.error("加载模板文件失败，使用动态创建的方式", e);
            return createDefaultWorkbook();
        }
    }
    
    /**
     * 创建默认的工作簿（备用方案）
     */
    private Workbook createDefaultWorkbook() throws Exception {
        Workbook workbook = new Workbook();
        Worksheet worksheet = workbook.getWorksheets().get(0);
        worksheet.setName("经营调度会看板");
        
        // 设置基本的列宽
        for (int i = 0; i <= 10; i++) {
            worksheet.getCells().setColumnWidth(i, 15);
        }
        
        return workbook;
    }
    
    /**
     * 填充公司汇总表格
     */
    private void fillCompanySummaryTable(Cells cells, List<Object[]> summaryData, 
                                       CellPosition startPosition, List<String> columnHeaders) {
        int currentRow = startPosition.getRow();
        
        // 填充数据行
        for (Object[] row : summaryData) {
            if (currentRow - startPosition.getRow() >= 10) {
                break; // 限制显示行数
            }
            
            for (int col = 0; col < row.length && col < columnHeaders.size(); col++) {
                CellPosition cellPos = new CellPosition(currentRow, startPosition.getColumn() + col);
                
                if (row[col] instanceof BigDecimal) {
                    // 金额类数据转换为万元
                    BigDecimal amount = (BigDecimal) row[col];
                    setCellValue(cells, cellPos, amount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                } else {
                    setCellValue(cells, cellPos, row[col]);
                }
            }
            currentRow++;
        }
    }
    
    /**
     * 填充明细表格
     */
    private void fillDetailTable(Cells cells, List<Object[]> detailData, CellPosition startPosition, String tableType) {
        int currentRow = startPosition.getRow();
        
        for (Object[] row : detailData) {
            if (currentRow - startPosition.getRow() >= 20) {
                break; // 限制显示行数
            }
            
            // 根据表格类型填充不同的列
            if ("存量债权".equals(tableType)) {
                // 存量债权明细：管理公司, 债权人, 债务人, 本月末债权余额, 本月处置债权, 案件名称
                setCellValue(cells, new CellPosition(currentRow, startPosition.getColumn()), row[0]); // 管理公司
                setCellValue(cells, new CellPosition(currentRow, startPosition.getColumn() + 1), row[1]); // 债权人
                setCellValue(cells, new CellPosition(currentRow, startPosition.getColumn() + 2), row[2]); // 债务人
                setCellValue(cells, new CellPosition(currentRow, startPosition.getColumn() + 3), 
                           toBigDecimal(row[3]).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP)); // 债权余额(万元)
            } else if ("新增债权".equals(tableType)) {
                // 新增债权明细：管理公司, 债权人, 债务人, 累计新增, 剩余余额, 案件名称
                setCellValue(cells, new CellPosition(currentRow, startPosition.getColumn()), row[0]); // 管理公司
                setCellValue(cells, new CellPosition(currentRow, startPosition.getColumn() + 1), row[1]); // 债权人
                setCellValue(cells, new CellPosition(currentRow, startPosition.getColumn() + 2), row[2]); // 债务人
                setCellValue(cells, new CellPosition(currentRow, startPosition.getColumn() + 3), 
                           toBigDecimal(row[3]).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP)); // 累计新增(万元)
            }
            currentRow++;
        }
    }
    
    /**
     * 安全地设置单元格值
     */
    private void setCellValue(Cells cells, CellPosition position, Object value) {
        try {
            Cell cell = cells.get(position.getRow(), position.getColumn());
            if (value instanceof BigDecimal) {
                cell.setValue(((BigDecimal) value).doubleValue());
            } else if (value instanceof Number) {
                cell.setValue(((Number) value).doubleValue());
            } else if (value != null) {
                cell.setValue(value.toString());
            } else {
                cell.setValue("");
            }
        } catch (Exception e) {
            logger.warn("设置单元格值失败 [{}]: {}", position.toExcelNotation(), e.getMessage());
        }
    }
    
    // ==================== 数据结构和工具类 ====================
    
    /**
     * 经营调度会看板数据束
     */
    public static class ManagementBoardDataBundle {
        private BigDecimal stockDebtBegin = BigDecimal.ZERO;                // 期初存量债权
        private BigDecimal yearCumulativeNewDebt = BigDecimal.ZERO;         // 本年累计新增
        private Object[] disposalMethods;                                  // 处置方式统计
        private List<Object[]> stockCompanySummary = new ArrayList<>();    // 存量债权公司汇总
        private List<Object[]> newCompanySummary = new ArrayList<>();      // 新增债权公司汇总

        // Getters and Setters
        public BigDecimal getStockDebtBegin() { return stockDebtBegin; }
        public void setStockDebtBegin(BigDecimal stockDebtBegin) { this.stockDebtBegin = stockDebtBegin; }

        public BigDecimal getYearCumulativeNewDebt() { return yearCumulativeNewDebt; }
        public void setYearCumulativeNewDebt(BigDecimal yearCumulativeNewDebt) { this.yearCumulativeNewDebt = yearCumulativeNewDebt; }

        public Object[] getDisposalMethods() { return disposalMethods; }
        public void setDisposalMethods(Object[] disposalMethods) { this.disposalMethods = disposalMethods; }

        public List<Object[]> getStockCompanySummary() { return stockCompanySummary; }
        public void setStockCompanySummary(List<Object[]> stockCompanySummary) { this.stockCompanySummary = stockCompanySummary; }

        public List<Object[]> getNewCompanySummary() { return newCompanySummary; }
        public void setNewCompanySummary(List<Object[]> newCompanySummary) { this.newCompanySummary = newCompanySummary; }
    }
    
    /**
     * Excel单元格位置类
     */
    public static class CellPosition {
        private int row;    // 行索引（从0开始）
        private int column; // 列索引（从0开始）
        
        public CellPosition(int row, int column) {
            this.row = row;
            this.column = column;
        }
        
        /**
         * 转换为Excel字母-数字表示法
         */
        public String toExcelNotation() {
            return getColumnLetter(column) + (row + 1);
        }
        
        private String getColumnLetter(int column) {
            StringBuilder result = new StringBuilder();
            while (column >= 0) {
                result.insert(0, (char) ('A' + column % 26));
                column = column / 26 - 1;
            }
            return result.toString();
        }
        
        public int getRow() { return row; }
        public int getColumn() { return column; }
    }
    
    
    
    
    
    /**
     * 生成安全的文件名
     */
    private String generateSafeFilename(String originalFilename) {
        try {
            // 方法1：URL编码（推荐）
            String encoded = URLEncoder.encode(originalFilename, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");
            
            // 方法2：如果浏览器不支持中文，提供备用英文名
            String fallbackName = originalFilename
                .replaceAll("[^\\w\\.\\-]", "_")  // 替换特殊字符
                .replaceAll("_{2,}", "_");       // 合并多个下划线
            
            logger.debug("文件名编码: 原始={}, 编码={}, 备用={}", originalFilename, encoded, fallbackName);
            
            return encoded;
            
        } catch (Exception e) {
            logger.warn("文件名编码失败，使用备用方案: {}", e.getMessage());
            // 备用方案：生成简单的英文文件名
            LocalDateTime now = LocalDateTime.now();
            return String.format("ManagementBoard_%s.xlsx", 
                now.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        }
    }
    
    // 工具方法
    private BigDecimal toBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        return BigDecimal.ZERO;
    }
    
    private Long toLong(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }
}