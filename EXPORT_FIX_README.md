# 逾期债权明细表导出功能Linux部署修复

## 问题描述

在Linux环境部署后，逾期债权明细表导出功能返回空表，但本地开发环境正常。

## 根本原因

`DatabaseUtils.java` 中硬编码了数据库连接配置：
- 硬编码主机: `localhost:3306`
- 硬编码密码: `Zlb&198838`

这导致Linux环境中使用了错误的数据库连接参数。

## 修复方案

### 1. 核心修复

修改 `shared/common/src/main/java/com/laoshu198838/util/database/DatabaseUtils.java`:

```java
// 修复前 (硬编码)
private static final String JDBC_URL_TEMPLATE = "******************************?...";
private static final String JDBC_PASSWORD = "Zlb&198838";

// 修复后 (环境变量)
private static final String DB_HOST = System.getenv("DB_HOST") != null ? System.getenv("DB_HOST") : "localhost";
private static final String DB_PORT = System.getenv("DB_PORT") != null ? System.getenv("DB_PORT") : "3306";
private static final String JDBC_URL_TEMPLATE = String.format("**********************?...", DB_HOST, DB_PORT);
private static final String JDBC_PASSWORD = System.getenv("DB_PASSWORD") != null ? System.getenv("DB_PASSWORD") : "Zlb&198838";
```

### 2. 环境变量支持

现在支持以下环境变量：
- `DB_HOST`: 数据库主机地址 (默认: localhost)
- `DB_PORT`: 数据库端口 (默认: 3306) 
- `DB_USERNAME`: 数据库用户名 (默认: root)
- `DB_PASSWORD`: 数据库密码 (默认: Zlb&198838)

### 3. 调试增强

增加了数据库连接调试信息，便于排查生产环境问题。

## 部署指南

### 方式1: 使用配置脚本 (推荐)

```bash
# 运行配置脚本
./scripts/deploy/configure-linux-env.sh

# 按提示输入数据库配置信息
# 脚本会自动配置系统环境变量
```

### 方式2: 手动配置环境变量

```bash
# 添加到 /etc/environment
echo "DB_HOST=your_db_host" >> /etc/environment
echo "DB_PORT=3306" >> /etc/environment  
echo "DB_USERNAME=your_username" >> /etc/environment
echo "DB_PASSWORD=your_password" >> /etc/environment

# 重新登录或source环境变量
source /etc/environment
```

### 方式3: Systemd服务配置

```bash
# 创建服务配置目录
mkdir -p /etc/systemd/system/financial-backend.service.d/

# 创建环境变量配置
cat > /etc/systemd/system/financial-backend.service.d/override.conf << EOF
[Service]
Environment=DB_HOST=your_db_host
Environment=DB_PORT=3306
Environment=DB_USERNAME=your_username
Environment=DB_PASSWORD=your_password
EOF

# 重新加载服务配置
systemctl daemon-reload
systemctl restart financial-backend
```

### 方式4: Docker Compose

参考 `docker-compose.env.yml` 文件配置容器环境变量。

## 验证修复

### 快速验证

```bash
# 运行验证脚本
./scripts/deploy/verify-export-fix.sh
```

### 手动验证

1. **检查环境变量**:
   ```bash
   echo $DB_HOST $DB_PORT $DB_USERNAME
   ```

2. **测试数据库连接**:
   ```bash
   mysql -h$DB_HOST -P$DB_PORT -u$DB_USERNAME -p
   ```

3. **测试导出API**:
   ```bash
   curl "http://localhost:8080/api/export/overdue-debt-details?year=2025&month=6&company=全部" \
        --output test_export.xlsx
   ```

4. **检查导出结果**:
   ```bash
   ls -lh test_export.xlsx
   file test_export.xlsx
   ```

## 应用启动

### 环境变量启动

```bash
export DB_HOST=your_db_host
export DB_PORT=3306
export DB_USERNAME=your_username
export DB_PASSWORD=your_password
mvn spring-boot:run -pl api-gateway
```

### 使用启动脚本

```bash
# 配置脚本会自动生成启动脚本
./start-financial-system.sh
```

## 故障排查

### 1. 导出仍然为空

检查以下项目:
- 数据库中是否有数据: `SELECT COUNT(*) FROM 减值准备表;`
- 导出API参数是否正确
- 应用日志中的数据库连接信息

### 2. 数据库连接失败

- 确认数据库服务运行: `systemctl status mysql`
- 检查网络连通性: `telnet $DB_HOST $DB_PORT`
- 验证用户权限: `mysql -h$DB_HOST -u$DB_USERNAME -p`

### 3. 应用启动失败

- 检查环境变量: `env | grep DB_`
- 查看应用日志: `tail -f logs/application.log`
- 验证Java版本和Maven配置

## 文件清单

修复相关的文件:

- `shared/common/src/main/java/com/laoshu198838/util/database/DatabaseUtils.java` - 核心修复
- `scripts/deploy/configure-linux-env.sh` - 环境配置脚本
- `scripts/deploy/verify-export-fix.sh` - 验证脚本
- `docker-compose.env.yml` - Docker环境配置示例
- `EXPORT_FIX_README.md` - 本说明文档

## 后续建议

1. **监控**: 添加数据库连接监控
2. **日志**: 改进日志记录，使用SLF4J替代System.out.println
3. **配置**: 考虑使用Spring Boot配置文件管理数据库连接
4. **测试**: 增加集成测试覆盖导出功能

---

**修复状态**: ✅ 已完成  
**修复版本**: v1.0.1  
**修复日期**: 2025-08-20  
**验证通过**: 环境变量配置 + 导出功能测试