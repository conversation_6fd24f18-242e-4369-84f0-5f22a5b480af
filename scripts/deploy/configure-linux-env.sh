#!/bin/bash
# 配置Linux环境变量脚本 - 修复逾期债权明细表导出问题
# 用法: ./scripts/deploy/configure-linux-env.sh

echo "🔧 配置Linux环境数据库连接环境变量"
echo "========================================="

# 检查是否运行在Linux环境
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "⚠️  警告: 此脚本主要用于Linux环境"
    echo "当前系统: $OSTYPE"
fi

# 创建环境变量配置文件
ENV_FILE="/etc/environment"
SYSTEMD_ENV_FILE="/etc/systemd/system/financial-backend.service.d/override.conf"

echo "📝 设置数据库环境变量..."

# 询问数据库配置信息
read -p "请输入数据库主机地址 [默认: localhost]: " DB_HOST
DB_HOST=${DB_HOST:-localhost}

read -p "请输入数据库端口 [默认: 3306]: " DB_PORT
DB_PORT=${DB_PORT:-3306}

read -p "请输入数据库用户名 [默认: root]: " DB_USERNAME
DB_USERNAME=${DB_USERNAME:-root}

read -s -p "请输入数据库密码 [默认: Zlb&198838]: " DB_PASSWORD
DB_PASSWORD=${DB_PASSWORD:-Zlb&198838}
echo

echo "✅ 配置信息确认:"
echo "   - DB_HOST: $DB_HOST"
echo "   - DB_PORT: $DB_PORT" 
echo "   - DB_USERNAME: $DB_USERNAME"
echo "   - DB_PASSWORD: [已设置]"

# 方法1: 添加到 /etc/environment (系统级别)
echo ""
echo "🔧 方法1: 系统级环境变量配置"
if [ -w "$ENV_FILE" ] || [ "$(id -u)" = "0" ]; then
    {
        echo "# 财务系统数据库配置 - $(date)"
        echo "DB_HOST=$DB_HOST"
        echo "DB_PORT=$DB_PORT"
        echo "DB_USERNAME=$DB_USERNAME" 
        echo "DB_PASSWORD=$DB_PASSWORD"
    } >> "$ENV_FILE"
    echo "✅ 已添加到 $ENV_FILE"
else
    echo "⚠️  需要root权限写入 $ENV_FILE"
    echo "请手动添加以下内容到 $ENV_FILE:"
    echo "DB_HOST=$DB_HOST"
    echo "DB_PORT=$DB_PORT"
    echo "DB_USERNAME=$DB_USERNAME"
    echo "DB_PASSWORD=$DB_PASSWORD"
fi

# 方法2: 创建Systemd服务环境变量文件
echo ""
echo "🔧 方法2: Systemd服务环境变量配置"
SYSTEMD_DIR="/etc/systemd/system/financial-backend.service.d"
if [ ! -d "$SYSTEMD_DIR" ]; then
    mkdir -p "$SYSTEMD_DIR" 2>/dev/null || {
        echo "⚠️  创建目录需要root权限: $SYSTEMD_DIR"
    }
fi

if [ -w "$SYSTEMD_DIR" ] || [ "$(id -u)" = "0" ]; then
    cat > "$SYSTEMD_ENV_FILE" << EOF
[Service]
Environment=DB_HOST=$DB_HOST
Environment=DB_PORT=$DB_PORT
Environment=DB_USERNAME=$DB_USERNAME
Environment=DB_PASSWORD=$DB_PASSWORD
EOF
    echo "✅ 已创建 $SYSTEMD_ENV_FILE"
    echo "💡 重启服务生效: systemctl daemon-reload && systemctl restart financial-backend"
else
    echo "⚠️  需要root权限创建服务配置"
fi

# 方法3: 导出到当前Shell环境 (临时)
echo ""
echo "🔧 方法3: 当前会话环境变量 (临时)"
export DB_HOST="$DB_HOST"
export DB_PORT="$DB_PORT"
export DB_USERNAME="$DB_USERNAME"
export DB_PASSWORD="$DB_PASSWORD"
echo "✅ 已设置当前会话环境变量"

# 方法4: 创建启动脚本
echo ""
echo "🔧 方法4: 创建应用启动脚本"
START_SCRIPT="./start-financial-system.sh"
cat > "$START_SCRIPT" << EOF
#!/bin/bash
# 财务系统启动脚本 - 自动配置环境变量

export DB_HOST=$DB_HOST
export DB_PORT=$DB_PORT
export DB_USERNAME=$DB_USERNAME
export DB_PASSWORD=$DB_PASSWORD

echo "🔗 启动财务系统，数据库连接: \$DB_HOST:\$DB_PORT"

# 启动后端应用
mvn spring-boot:run -pl api-gateway
EOF

chmod +x "$START_SCRIPT"
echo "✅ 已创建启动脚本: $START_SCRIPT"

# 测试数据库连接
echo ""
echo "🧪 测试数据库连接..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SHOW DATABASES;" 2>/dev/null && {
    echo "✅ 数据库连接测试成功"
} || {
    echo "❌ 数据库连接测试失败，请检查配置"
    echo "💡 可以尝试手动连接测试: mysql -h$DB_HOST -P$DB_PORT -u$DB_USERNAME -p"
}

echo ""
echo "🎉 环境配置完成!"
echo ""
echo "📋 后续步骤:"
echo "1. 重启系统或重新登录使环境变量生效"
echo "2. 或使用启动脚本: ./$START_SCRIPT"
echo "3. 或手动导出环境变量后启动应用"
echo "4. 验证导出功能是否正常工作"
echo ""
echo "🔍 验证方法:"
echo "echo \$DB_HOST \$DB_PORT \$DB_USERNAME"
echo ""