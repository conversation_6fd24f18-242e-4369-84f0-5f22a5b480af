package com.laoshu198838.util.business;

import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 前80%筛选算法工具类
 * 实现按金额降序排列后取前80%且大于阈值的记录筛选
 * 
 * 核心规则：
 * 1. 按金额降序排序
 * 2. 取前80%的记录数量
 * 3. 再筛选金额大于指定阈值的记录
 * 4. 支持分组筛选和批量处理
 * 
 * <AUTHOR> Code
 * @since 2025-01-18
 */
@Component
public class Top80PercentFilter {

    /** 默认的最小金额阈值（100万元）*/
    public static final BigDecimal DEFAULT_MIN_AMOUNT = new BigDecimal("1000000");

    /**
     * 筛选前80%数据
     * 
     * @param dataList 已排序的数据列表
     * @param amountExtractor 金额提取函数
     * @param minAmount 最小金额阈值（元）
     * @return 筛选后的数据列表
     */
    public <T> List<T> filterTop80Percent(List<T> dataList, 
                                         Function<T, BigDecimal> amountExtractor,
                                         BigDecimal minAmount) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 1. 按金额降序排序
        List<T> sortedList = dataList.stream()
            .sorted((a, b) -> {
                BigDecimal amountA = amountExtractor.apply(a);
                BigDecimal amountB = amountExtractor.apply(b);
                if (amountA == null) amountA = BigDecimal.ZERO;
                if (amountB == null) amountB = BigDecimal.ZERO;
                return amountB.compareTo(amountA);
            })
            .collect(Collectors.toList());
        
        // 2. 计算80%的记录数量
        int totalCount = sortedList.size();
        int top80Count = (int) Math.ceil(totalCount * 0.8);
        
        // 3. 取前80%的记录
        List<T> top80Percent = sortedList.subList(0, Math.min(top80Count, totalCount));
        
        // 4. 再筛选金额大于阈值的记录
        List<T> filtered = top80Percent.stream()
            .filter(item -> {
                BigDecimal amount = amountExtractor.apply(item);
                return amount != null && amount.compareTo(minAmount) > 0;
            })
            .collect(Collectors.toList());
        
        return filtered;
    }

    /**
     * 筛选前80%数据（使用默认阈值100万）
     * 
     * @param dataList 数据列表
     * @param amountExtractor 金额提取函数
     * @return 筛选后的数据列表
     */
    public <T> List<T> filterTop80Percent(List<T> dataList, 
                                         Function<T, BigDecimal> amountExtractor) {
        return filterTop80Percent(dataList, amountExtractor, DEFAULT_MIN_AMOUNT);
    }

    /**
     * 按分组筛选前80%
     * 每个分组内部独立进行前80%筛选
     * 
     * @param dataList 数据列表
     * @param groupExtractor 分组键提取函数
     * @param amountExtractor 金额提取函数
     * @param minAmount 最小金额阈值
     * @return 按分组筛选后的数据映射
     */
    public <T, K> Map<K, List<T>> filterTop80PercentByGroup(
            List<T> dataList,
            Function<T, K> groupExtractor,
            Function<T, BigDecimal> amountExtractor,
            BigDecimal minAmount) {
        
        if (dataList == null || dataList.isEmpty()) {
            return new HashMap<>();
        }
        
        return dataList.stream()
            .collect(Collectors.groupingBy(groupExtractor))
            .entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> filterTop80Percent(entry.getValue(), amountExtractor, minAmount)
            ));
    }

    /**
     * 按分组筛选前80%（使用默认阈值）
     */
    public <T, K> Map<K, List<T>> filterTop80PercentByGroup(
            List<T> dataList,
            Function<T, K> groupExtractor,
            Function<T, BigDecimal> amountExtractor) {
        return filterTop80PercentByGroup(dataList, groupExtractor, amountExtractor, DEFAULT_MIN_AMOUNT);
    }

    /**
     * 专用于债权明细筛选
     * 使用标准的债权明细DTO筛选逻辑
     * 
     * @param debtList 债权明细列表
     * @param amountExtractor 金额提取函数
     * @param minAmountInWan 最小金额阈值（万元）
     * @return 筛选后的债权列表
     */
    public <T> List<T> filterDebtDetails(List<T> debtList, 
                                        Function<T, BigDecimal> amountExtractor,
                                        BigDecimal minAmountInWan) {
        // 将万元转换为元
        BigDecimal minAmountInYuan = minAmountInWan.multiply(new BigDecimal("10000"));
        return filterTop80Percent(debtList, amountExtractor, minAmountInYuan);
    }

    /**
     * 专用于债权明细筛选（使用默认100万元阈值）
     */
    public <T> List<T> filterDebtDetails(List<T> debtList, 
                                        Function<T, BigDecimal> amountExtractor) {
        return filterDebtDetails(debtList, amountExtractor, new BigDecimal("100"));
    }

    /**
     * 按管理公司分组筛选债权前80%
     * 
     * @param debtList 债权列表
     * @param companyExtractor 管理公司提取函数
     * @param amountExtractor 金额提取函数
     * @param minAmountInWan 最小金额阈值（万元）
     * @return 按公司分组的筛选结果
     */
    public <T> Map<String, List<T>> filterTop80PercentByCompany(
            List<T> debtList,
            Function<T, String> companyExtractor,
            Function<T, BigDecimal> amountExtractor,
            BigDecimal minAmountInWan) {
        return filterTop80PercentByGroup(debtList, companyExtractor, amountExtractor, 
                                       minAmountInWan.multiply(new BigDecimal("10000")));
    }

    /**
     * 按管理公司分组筛选债权前80%（使用默认阈值）
     */
    public <T> Map<String, List<T>> filterTop80PercentByCompany(
            List<T> debtList,
            Function<T, String> companyExtractor,
            Function<T, BigDecimal> amountExtractor) {
        return filterTop80PercentByCompany(debtList, companyExtractor, amountExtractor, new BigDecimal("100"));
    }

    /**
     * 计算筛选统计信息
     * 
     * @param originalList 原始数据列表
     * @param filteredList 筛选后的数据列表
     * @param amountExtractor 金额提取函数
     * @return 筛选统计信息
     */
    public <T> FilterStatistics calculateFilterStatistics(
            List<T> originalList,
            List<T> filteredList,
            Function<T, BigDecimal> amountExtractor) {
        
        FilterStatistics stats = new FilterStatistics();
        
        if (originalList == null || originalList.isEmpty()) {
            return stats;
        }
        
        // 原始数据统计
        stats.setOriginalCount(originalList.size());
        BigDecimal originalTotalAmount = originalList.stream()
            .map(amountExtractor)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setOriginalTotalAmount(originalTotalAmount);
        
        // 筛选后数据统计
        if (filteredList != null) {
            stats.setFilteredCount(filteredList.size());
            BigDecimal filteredTotalAmount = filteredList.stream()
                .map(amountExtractor)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            stats.setFilteredTotalAmount(filteredTotalAmount);
            
            // 计算比例
            stats.setFilteredCountRatio(filteredList.size() * 100.0 / originalList.size());
            if (originalTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
                stats.setFilteredAmountRatio(
                    filteredTotalAmount.multiply(new BigDecimal("100"))
                        .divide(originalTotalAmount, 2, BigDecimal.ROUND_HALF_UP).doubleValue());
            }
        }
        
        return stats;
    }

    /**
     * 批量筛选多个数据集
     * 
     * @param dataSets 数据集映射
     * @param amountExtractor 金额提取函数
     * @param minAmount 最小金额阈值
     * @return 筛选后的数据集映射
     */
    public <T, K> Map<K, List<T>> batchFilter(
            Map<K, List<T>> dataSets,
            Function<T, BigDecimal> amountExtractor,
            BigDecimal minAmount) {
        
        return dataSets.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> filterTop80Percent(entry.getValue(), amountExtractor, minAmount)
            ));
    }

    /**
     * 筛选统计信息
     */
    public static class FilterStatistics {
        private int originalCount = 0;                          // 原始记录数
        private int filteredCount = 0;                          // 筛选后记录数
        private double filteredCountRatio = 0.0;               // 记录数筛选比例(%)
        private BigDecimal originalTotalAmount = BigDecimal.ZERO;     // 原始总金额
        private BigDecimal filteredTotalAmount = BigDecimal.ZERO;     // 筛选后总金额
        private double filteredAmountRatio = 0.0;              // 金额筛选比例(%)

        // Getters and Setters
        public int getOriginalCount() { return originalCount; }
        public void setOriginalCount(int originalCount) { this.originalCount = originalCount; }

        public int getFilteredCount() { return filteredCount; }
        public void setFilteredCount(int filteredCount) { this.filteredCount = filteredCount; }

        public double getFilteredCountRatio() { return filteredCountRatio; }
        public void setFilteredCountRatio(double filteredCountRatio) { this.filteredCountRatio = filteredCountRatio; }

        public BigDecimal getOriginalTotalAmount() { return originalTotalAmount; }
        public void setOriginalTotalAmount(BigDecimal originalTotalAmount) { this.originalTotalAmount = originalTotalAmount; }

        public BigDecimal getFilteredTotalAmount() { return filteredTotalAmount; }
        public void setFilteredTotalAmount(BigDecimal filteredTotalAmount) { this.filteredTotalAmount = filteredTotalAmount; }

        public double getFilteredAmountRatio() { return filteredAmountRatio; }
        public void setFilteredAmountRatio(double filteredAmountRatio) { this.filteredAmountRatio = filteredAmountRatio; }

        @Override
        public String toString() {
            return String.format("FilterStatistics{原始记录=%d, 筛选后记录=%d, 记录筛选率=%.1f%%, 金额筛选率=%.1f%%}",
                    originalCount, filteredCount, filteredCountRatio, filteredAmountRatio);
        }
    }

    /**
     * 债权明细DTO简单实现（用于演示）
     */
    public static class DebtDetailDTO {
        private String company;         // 管理公司
        private String creditor;        // 债权人
        private String debtor;          // 债务人
        private BigDecimal amount;      // 金额
        private String caseName;        // 案件名称

        public DebtDetailDTO() {}

        public DebtDetailDTO(String company, String creditor, String debtor, BigDecimal amount) {
            this.company = company;
            this.creditor = creditor;
            this.debtor = debtor;
            this.amount = amount;
        }

        // Getters and Setters
        public String getCompany() { return company; }
        public void setCompany(String company) { this.company = company; }

        public String getCreditor() { return creditor; }
        public void setCreditor(String creditor) { this.creditor = creditor; }

        public String getDebtor() { return debtor; }
        public void setDebtor(String debtor) { this.debtor = debtor; }

        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }

        public String getCaseName() { return caseName; }
        public void setCaseName(String caseName) { this.caseName = caseName; }

        @Override
        public String toString() {
            return String.format("DebtDetailDTO{公司='%s', 债权人='%s', 债务人='%s', 金额=%.2f万}",
                    company, creditor, debtor, amount != null ? amount.divide(new BigDecimal("10000")).doubleValue() : 0);
        }
    }
}