package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtSummary;
import com.laoshu198838.entity.overdue_debt.OverdueDebtSummary.OverdueDebtSummaryKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 逾期债权明细查询数据访问接口
 * 处理复杂的债权明细查询
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface OverdueDebtDetailRepository extends JpaRepository<OverdueDebtSummary, OverdueDebtSummaryKey> {

    //    根据传入的参数查询处置金额明细表
    @Query(value = """
                   # noinspection SqlAggregatesForFile @ column/"累计处置金额"
                               SELECT *
                               FROM (
                                   SELECT
                                       管理公司,
                                       债权人,
                                       债务人,
                                       期间,
                                       是否涉诉,
                                       SUM(CASE
                                           WHEN :month IS NULL OR :month = '所有月份' OR :month = '全部' THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 12 AND 月份 BETWEEN 1 AND 12 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 11 AND 月份 BETWEEN 1 AND 11 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 10 AND 月份 BETWEEN 1 AND 10 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 9 AND 月份 BETWEEN 1 AND 9 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 8 AND 月份 BETWEEN 1 AND 8 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 7 AND 月份 BETWEEN 1 AND 7 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 6 AND 月份 BETWEEN 1 AND 6 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 5 AND 月份 BETWEEN 1 AND 5 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 4 AND 月份 BETWEEN 1 AND 4 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 3 AND 月份 BETWEEN 1 AND 3 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 2 AND 月份 BETWEEN 1 AND 2 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 1 AND 月份 = 1 THEN IFNULL(每月处置金额, 0)
                                           ELSE 0
                                       END) AS 累计处置金额
                                   FROM 处置表
                                   WHERE (:year IS NULL OR 年份 = :year)
                                   AND (:company IS NULL OR :company = '所有公司' OR 管理公司 = :company)
                                   GROUP BY 管理公司, 债权人, 债务人, 期间, 是否涉诉
                                   HAVING 累计处置金额 > 0
                                   ORDER BY 累计处置金额 DESC
                               ) AS derived_table
                   """, nativeQuery = true)
    List<Map<String, Object>> findReductionDebtDetailList(
            @Param("year") String year,
            @Param("month") String month,
            @Param("company") String company);
    
    /**
     * 查询符合条件的债权数据用于经营调度会看板
     * 新增表使用月份列存储每月新增金额，需要计算累计金额
     * @param year 年份
     * @param month 月份
     * @param minAmount 最小金额（元）
     * @return 债权数据列表
     */
    @Query(value = """
        SELECT 
            a.序号 as debt_id,
            a.债权人 as creditor,
            a.债务人 as debtor,
            a.新增金额 as debt_amount,
            a.债权余额 as debt_balance,
            a.管理公司 as company,
            a.是否涉诉 as is_litigation,
            a.期间 as period,
            CASE 
                WHEN :month = '1' THEN a.`1月`
                WHEN :month = '2' THEN a.`1月` + a.`2月`
                WHEN :month = '3' THEN a.`1月` + a.`2月` + a.`3月`
                WHEN :month = '4' THEN a.`1月` + a.`2月` + a.`3月` + a.`4月`
                WHEN :month = '5' THEN a.`1月` + a.`2月` + a.`3月` + a.`4月` + a.`5月`
                WHEN :month = '6' THEN a.`1月` + a.`2月` + a.`3月` + a.`4月` + a.`5月` + a.`6月`
                WHEN :month = '7' THEN a.`1月` + a.`2月` + a.`3月` + a.`4月` + a.`5月` + a.`6月` + a.`7月`
                WHEN :month = '8' THEN a.`1月` + a.`2月` + a.`3月` + a.`4月` + a.`5月` + a.`6月` + a.`7月` + a.`8月`
                WHEN :month = '9' THEN a.`1月` + a.`2月` + a.`3月` + a.`4月` + a.`5月` + a.`6月` + a.`7月` + a.`8月` + a.`9月`
                WHEN :month = '10' THEN a.`1月` + a.`2月` + a.`3月` + a.`4月` + a.`5月` + a.`6月` + a.`7月` + a.`8月` + a.`9月` + a.`10月`
                WHEN :month = '11' THEN a.`1月` + a.`2月` + a.`3月` + a.`4月` + a.`5月` + a.`6月` + a.`7月` + a.`8月` + a.`9月` + a.`10月` + a.`11月`
                WHEN :month = '12' THEN a.`1月` + a.`2月` + a.`3月` + a.`4月` + a.`5月` + a.`6月` + a.`7月` + a.`8月` + a.`9月` + a.`10月` + a.`11月` + a.`12月`
                ELSE a.新增金额
            END as cumulative_amount
        FROM 新增表 a
        WHERE (:year IS NULL OR a.年份 = :year)
          AND a.新增金额 >= :minAmount
          AND a.债权余额 > 0
        ORDER BY a.新增金额 DESC
    """, nativeQuery = true)
    List<Map<String, Object>> findDebtsByYearMonthAndMinAmount(
            @Param("year") String year,
            @Param("month") String month,
            @Param("minAmount") BigDecimal minAmount);

    // ==================== 经营调度会看板专用查询方法 ====================

    /**
     * 新增债权累计数据查询（经营调度会看板）
     * 查询新增债权的累计新增和累计处置数据，计算剩余余额
     *
     * @param year 年份（字符串）
     * @param monthInt 月份（整数，累计到该月份）
     * @return 新增债权累计数据 [管理公司, 债权人, 债务人, 累计新增, 累计处置, 剩余余额]
     */
    @Query(value = """
        SELECT n.管理公司, n.债权人, n.债务人, 
        SUM(CASE WHEN CAST(n.月份 AS SIGNED) <= :monthInt THEN n.新增金额 ELSE 0 END) as cumulativeNew, 
        SUM(CASE WHEN CAST(n.月份 AS SIGNED) <= :monthInt THEN n.处置金额 ELSE 0 END) as cumulativeDisposal, 
        (SUM(CASE WHEN CAST(n.月份 AS SIGNED) <= :monthInt THEN n.新增金额 ELSE 0 END) - 
         SUM(CASE WHEN CAST(n.月份 AS SIGNED) <= :monthInt THEN n.处置金额 ELSE 0 END)) as remainingBalance 
        FROM 新增表 n 
        WHERE n.年份 = :year 
        GROUP BY n.管理公司, n.债权人, n.债务人
        HAVING cumulativeNew > 0 
        ORDER BY cumulativeNew DESC
        """, nativeQuery = true)
    List<Object[]> findCumulativeNewDebtData(@Param("year") String year,
                                            @Param("monthInt") Integer monthInt);

    /**
     * 新增债权年度汇总查询（经营调度会看板）
     * 查询指定年份累计到指定月份的新增债权总金额
     *
     * @param year 年份（字符串）
     * @param monthInt 月份（整数，累计到该月份）
     * @return 年度新增债权总金额
     */
    @Query(value = """
        SELECT COALESCE(SUM(CASE WHEN CAST(月份 AS SIGNED) <= :monthInt THEN 新增金额 ELSE 0 END), 0) as totalNew
        FROM 新增表 
        WHERE 年份 = :year
        """, nativeQuery = true)
    BigDecimal findYearCumulativeNewDebtAmount(@Param("year") String year,
                                              @Param("monthInt") Integer monthInt);

    /**
     * 新增债权公司汇总查询（经营调度会看板）
     * 按管理公司汇总新增债权的累计金额和剩余余额
     *
     * @param year 年份（字符串）
     * @param monthInt 月份（整数，累计到该月份）
     * @return 公司汇总数据 [管理公司, 累计新增, 累计处置, 剩余余额, 记录数]
     */
    @Query(value = """
        SELECT 管理公司, 
        SUM(CASE WHEN CAST(月份 AS SIGNED) <= :monthInt THEN 新增金额 ELSE 0 END) as cumulativeNew, 
        SUM(CASE WHEN CAST(月份 AS SIGNED) <= :monthInt THEN 处置金额 ELSE 0 END) as cumulativeDisposal, 
        (SUM(CASE WHEN CAST(月份 AS SIGNED) <= :monthInt THEN 新增金额 ELSE 0 END) - 
         SUM(CASE WHEN CAST(月份 AS SIGNED) <= :monthInt THEN 处置金额 ELSE 0 END)) as remainingBalance,
        COUNT(DISTINCT CONCAT(债权人, '-', 债务人)) as recordCount 
        FROM 新增表 
        WHERE 年份 = :year 
        GROUP BY 管理公司 
        HAVING cumulativeNew > 0 
        ORDER BY cumulativeNew DESC
        """, nativeQuery = true)
    List<Object[]> findNewDebtCompanySummary(@Param("year") String year,
                                            @Param("monthInt") Integer monthInt);

    /**
     * 新增债权明细查询支持筛选（经营调度会看板）
     * 查询新增债权明细数据，支持最小金额筛选
     *
     * @param year 年份（字符串）
     * @param monthInt 月份（整数，累计到该月份）
     * @param minAmount 最小金额阈值
     * @return 新增债权明细数据 [管理公司, 债权人, 债务人, 累计新增, 剩余余额, 案件名称]
     */
    @Query(value = """
        SELECT n.管理公司, n.债权人, n.债务人, 
        SUM(CASE WHEN CAST(n.月份 AS SIGNED) <= :monthInt THEN n.新增金额 ELSE 0 END) as cumulativeNew, 
        (SUM(CASE WHEN CAST(n.月份 AS SIGNED) <= :monthInt THEN n.新增金额 ELSE 0 END) - 
         SUM(CASE WHEN CAST(n.月份 AS SIGNED) <= :monthInt THEN n.处置金额 ELSE 0 END)) as remainingBalance,
        GROUP_CONCAT(DISTINCT n.案件名称 SEPARATOR ',') as caseNames
        FROM 新增表 n 
        WHERE n.年份 = :year 
        GROUP BY n.管理公司, n.债权人, n.债务人
        HAVING cumulativeNew > :minAmount 
        ORDER BY cumulativeNew DESC
        """, nativeQuery = true)
    List<Object[]> findNewDebtDetails(@Param("year") String year,
                                     @Param("monthInt") Integer monthInt,
                                     @Param("minAmount") BigDecimal minAmount);
}
