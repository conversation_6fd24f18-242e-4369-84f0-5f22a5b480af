/**
 * 组织架构管理页面
 *
 * 功能：
 * 1. 组织架构树形展示
 * 2. 合并规则配置
 * 3. 拖拽调整组织结构
 */

import React, { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  Box,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Alert
} from '@mui/material';
import SimpleTreeView from 'components/lucanet/SimpleTreeView';
import { Business as BusinessIcon } from '@mui/icons-material';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import MDButton from 'components/MDButton';
import DashboardPageLayout from 'components/layouts/DashboardPageLayout';

// 财务合并服务
import { lucaNetMockApi } from 'services/lucanet/mockApiService';

const OrganizationManagement = () => {
  const [organizations, setOrganizations] = useState([]);
  const [selectedOrg, setSelectedOrg] = useState(null);
  const [consolidationRules, setConsolidationRules] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 合并方法标签颜色
  const getMethodColor = method => {
    switch (method) {
      case 'FULL':
        return 'success';
      case 'PROPORTIONAL':
        return 'warning';
      case 'EQUITY':
        return 'info';
      default:
        return 'default';
    }
  };

  // 加载组织数据
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [orgData, rulesData] = await Promise.all([
          lucaNetMockApi.getOrganizations(),
          lucaNetMockApi.getConsolidationRules()
        ]);
        setOrganizations(orgData);
        setConsolidationRules(rulesData);
        setError(null);
      } catch (err) {
        setError('加载组织数据失败: ' + err.message);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // 渲染组织树节点额外内容
  const renderNodeExtra = node => {
    const rules = consolidationRules[node.id] || {};

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, ml: 1 }}>
        <Chip
          label={node.type}
          size="small"
          color={node.type === 'GROUP' ? 'primary' : 'default'}
          sx={{ fontSize: '0.7rem' }}
        />
        {rules.consolidationMethod && (
          <Chip
            label={rules.consolidationMethod}
            size="small"
            color={getMethodColor(rules.consolidationMethod)}
            sx={{ fontSize: '0.7rem' }}
          />
        )}
        {rules.ownershipPercentage && (
          <Chip
            label={`${rules.ownershipPercentage}%`}
            size="small"
            variant="outlined"
            sx={{ fontSize: '0.7rem' }}
          />
        )}
      </Box>
    );
  };

  // 处理合并规则更新
  const handleRuleUpdate = async (field, value) => {
    if (!selectedOrg) {
      return;
    }

    try {
      const updatedRules = {
        ...consolidationRules,
        [selectedOrg.id]: {
          ...consolidationRules[selectedOrg.id],
          [field]: value
        }
      };

      setConsolidationRules(updatedRules);

      // 模拟API保存
      await lucaNetMockApi.updateConsolidationRule(selectedOrg.id, updatedRules[selectedOrg.id]);
    } catch (err) {
      setError('更新合并规则失败: ' + err.message);
    }
  };

  if (loading) {
    return (
      <DashboardPageLayout>
        <MDBox py={3} textAlign="center">
          <MDTypography variant="h6">加载中...</MDTypography>
        </MDBox>
      </DashboardPageLayout>
    );
  }

  return (
    <DashboardPageLayout>
      <MDBox py={3}>
        <MDBox mb={3}>
          <MDTypography variant="h4" gutterBottom>
            组织架构管理
          </MDTypography>
          <MDTypography variant="body2" color="text">
            管理企业集团组织架构和财务合并规则配置
          </MDTypography>
        </MDBox>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* 左侧：组织架构树 */}
          <Grid item xs={12} md={7}>
            <Card sx={{ p: 3, height: '600px', overflow: 'auto' }}>
              <MDBox mb={2}>
                <MDTypography variant="h6">组织架构</MDTypography>
                <MDTypography variant="caption" color="text">
                  点击节点查看和配置合并规则
                </MDTypography>
              </MDBox>

              <SimpleTreeView
                data={organizations}
                onSelect={setSelectedOrg}
                defaultExpanded={organizations.map(org => org.id)}
                renderNode={renderNodeExtra}
              />
            </Card>
          </Grid>

          {/* 右侧：合并规则配置 */}
          <Grid item xs={12} md={5}>
            <Card sx={{ p: 3, height: '600px' }}>
              <MDBox mb={2}>
                <MDTypography variant="h6">合并规则配置</MDTypography>
                {selectedOrg ? (
                  <MDTypography variant="caption" color="text">
                    当前选择: {selectedOrg.name}
                  </MDTypography>
                ) : (
                  <MDTypography variant="caption" color="text">
                    请选择组织节点进行配置
                  </MDTypography>
                )}
              </MDBox>

              {selectedOrg ? (
                <Box>
                  {/* 基本信息 */}
                  <Box mb={3}>
                    <TextField
                      fullWidth
                      label="组织代码"
                      value={selectedOrg.code}
                      disabled
                      sx={{ mb: 2 }}
                    />
                    <TextField
                      fullWidth
                      label="组织名称"
                      value={selectedOrg.name}
                      disabled
                      sx={{ mb: 2 }}
                    />
                    <TextField fullWidth label="组织类型" value={selectedOrg.type} disabled />
                  </Box>

                  {/* 合并规则配置 */}
                  {selectedOrg.parentId && (
                    <Box>
                      <MDTypography variant="h6" gutterBottom>
                        合并规则
                      </MDTypography>

                      <TextField
                        fullWidth
                        label="持股比例 (%)"
                        type="number"
                        value={consolidationRules[selectedOrg.id]?.ownershipPercentage || ''}
                        onChange={e =>
                          handleRuleUpdate('ownershipPercentage', parseFloat(e.target.value))
                        }
                        inputProps={{ min: 0, max: 100, step: 0.01 }}
                        sx={{ mb: 2 }}
                      />

                      <FormControl fullWidth sx={{ mb: 2 }}>
                        <InputLabel>合并方法</InputLabel>
                        <Select
                          value={consolidationRules[selectedOrg.id]?.consolidationMethod || ''}
                          onChange={e => handleRuleUpdate('consolidationMethod', e.target.value)}
                          label="合并方法"
                        >
                          <MenuItem value="FULL">全额合并法</MenuItem>
                          <MenuItem value="PROPORTIONAL">比例合并法</MenuItem>
                          <MenuItem value="EQUITY">权益法</MenuItem>
                        </Select>
                      </FormControl>

                      <TextField
                        fullWidth
                        label="表决权比例 (%)"
                        type="number"
                        value={consolidationRules[selectedOrg.id]?.votingPercentage || ''}
                        onChange={e =>
                          handleRuleUpdate('votingPercentage', parseFloat(e.target.value))
                        }
                        inputProps={{ min: 0, max: 100, step: 0.01 }}
                        sx={{ mb: 2 }}
                      />

                      <TextField
                        fullWidth
                        label="生效日期"
                        type="date"
                        value={consolidationRules[selectedOrg.id]?.effectiveDate || '2025-01-01'}
                        onChange={e => handleRuleUpdate('effectiveDate', e.target.value)}
                        InputLabelProps={{ shrink: true }}
                        sx={{ mb: 3 }}
                      />

                      <MDButton
                        variant="gradient"
                        color="success"
                        fullWidth
                        onClick={() => {
                          // 保存规则的实际实现
                          console.log('保存合并规则', consolidationRules[selectedOrg.id]);
                        }}
                      >
                        保存合并规则
                      </MDButton>
                    </Box>
                  )}

                  {!selectedOrg.parentId && <Alert severity="info">根组织无需配置合并规则</Alert>}
                </Box>
              ) : (
                <Box textAlign="center" py={10}>
                  <BusinessIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                  <MDTypography variant="h6" color="text.secondary">
                    请选择左侧组织节点
                  </MDTypography>
                  <MDTypography variant="body2" color="text.disabled">
                    点击组织树中的节点来配置合并规则
                  </MDTypography>
                </Box>
              )}
            </Card>
          </Grid>
        </Grid>
      </MDBox>
    </DashboardPageLayout>
  );
};

export default OrganizationManagement;
