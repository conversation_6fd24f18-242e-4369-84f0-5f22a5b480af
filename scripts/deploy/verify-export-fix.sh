#!/bin/bash
# 验证逾期债权明细表导出修复脚本
# 用法: ./scripts/deploy/verify-export-fix.sh

echo "🧪 验证逾期债权明细表导出修复"
echo "================================="

# 检查环境变量
echo "📋 步骤1: 检查数据库环境变量"
env_vars=("DB_HOST" "DB_PORT" "DB_USERNAME" "DB_PASSWORD")
missing_vars=()

for var in "${env_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ 环境变量 $var 未设置"
        missing_vars+=("$var")
    else
        if [ "$var" = "DB_PASSWORD" ]; then
            echo "✅ $var: [已设置]"
        else
            echo "✅ $var: ${!var}"
        fi
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo ""
    echo "⚠️  警告: 以下环境变量未设置，将使用默认值:"
    printf '   - %s\n' "${missing_vars[@]}"
    echo ""
    echo "💡 如需设置，请运行: ./scripts/deploy/configure-linux-env.sh"
    echo ""
fi

# 检查应用是否运行
echo "📋 步骤2: 检查应用运行状态"
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health | grep -q "200"; then
    echo "✅ 后端应用运行正常 (端口8080)"
else
    echo "❌ 后端应用未运行或端口8080不可访问"
    echo "💡 请先启动应用: mvn spring-boot:run -pl api-gateway"
    echo "💡 或使用启动脚本: ./start-financial-system.sh"
    exit 1
fi

# 测试数据库连接
echo "📋 步骤3: 测试数据库连接"
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_USERNAME=${DB_USERNAME:-root}
DB_PASSWORD=${DB_PASSWORD:-Zlb&198838}

mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SHOW DATABASES;" >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接正常"
    
    # 检查核心数据表
    echo "   检查核心数据表..."
    tables=("减值准备表" "处置表")
    for table in "${tables[@]}"; do
        count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -D overdue_debt_db -se "SELECT COUNT(*) FROM \`$table\`" 2>/dev/null)
        if [ $? -eq 0 ]; then
            echo "   ✅ $table: $count 条记录"
        else
            echo "   ❌ $table: 无法访问"
        fi
    done
else
    echo "❌ 数据库连接失败"
    echo "💡 请检查数据库配置和连接信息"
    exit 1
fi

# 测试导出API
echo "📋 步骤4: 测试逾期债权明细表导出API"
export_url="http://localhost:8080/api/export/overdue-debt-details"
test_params="?year=2025&month=6&company=全部"

echo "   调用导出API: $export_url$test_params"
response=$(curl -s -w "%{http_code}" -o "/tmp/export_test.xlsx" "$export_url$test_params")

if [ "$response" = "200" ]; then
    echo "✅ 导出API调用成功 (HTTP 200)"
    
    # 检查导出文件
    if [ -f "/tmp/export_test.xlsx" ] && [ -s "/tmp/export_test.xlsx" ]; then
        file_size=$(ls -lh "/tmp/export_test.xlsx" | awk '{print $5}')
        echo "✅ 导出文件生成成功: $file_size"
        echo "   文件位置: /tmp/export_test.xlsx"
        
        # 简单检查文件是否为有效的Excel文件
        if file /tmp/export_test.xlsx | grep -q "Microsoft Excel"; then
            echo "✅ 导出文件格式正确 (Excel)"
        else
            echo "⚠️  导出文件格式可能有问题"
        fi
        
        rm -f /tmp/export_test.xlsx
        
    else
        echo "❌ 导出文件为空或未生成"
        echo "🔍 这可能表明导出功能仍有问题"
        exit 1
    fi
else
    echo "❌ 导出API调用失败 (HTTP $response)"
    echo "🔍 请检查应用日志查看详细错误信息"
    exit 1
fi

# 检查应用日志中的数据库连接信息
echo "📋 步骤5: 检查应用日志"
if [ -f "logs/application.log" ]; then
    echo "   查看最近的数据库连接日志..."
    grep -i "连接到数据库\|数据库连接\|DB_HOST\|尝试连接" logs/application.log | tail -5
elif command -v journalctl >/dev/null; then
    echo "   查看systemd日志中的数据库连接信息..."
    journalctl -u financial-backend --since "5 minutes ago" | grep -i "连接到数据库\|数据库连接\|DB_HOST\|尝试连接" | tail -5
else
    echo "   无法找到应用日志文件"
    echo "💡 可以查看控制台输出中的数据库连接信息"
fi

echo ""
echo "🎉 验证完成!"
echo ""
echo "✅ 修复验证结果:"
echo "   - 环境变量配置正确"
echo "   - 数据库连接正常"  
echo "   - 导出API功能正常"
echo "   - 导出文件生成成功"
echo ""
echo "💡 如果导出的数据仍然为空，请检查:"
echo "   1. 数据库中是否有测试数据"
echo "   2. 导出API的查询条件是否正确"
echo "   3. 应用日志中是否有其他错误信息"
echo ""