# Docker Compose 环境变量配置示例
# 修复逾期债权明细表Linux导出空表问题
version: '3.8'

services:
  financial-backend:
    build: .
    environment:
      - DB_HOST=mysql-db
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD:-Zlb&198838}
    depends_on:
      - mysql-db
    ports:
      - "8080:8080"
    networks:
      - financial-network

  mysql-db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-Zlb&198838}
      - MYSQL_DATABASE=overdue_debt_db
    volumes:
      - mysql-data:/var/lib/mysql
      - ./init-db:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306" 
    networks:
      - financial-network

  financial-frontend:
    build: ./FinancialSystem-web
    ports:
      - "3000:3000"
    depends_on:
      - financial-backend
    networks:
      - financial-network

volumes:
  mysql-data:

networks:
  financial-network:
    driver: bridge

# 使用方法:
# 1. 复制此文件为 docker-compose.override.yml
# 2. 根据实际环境修改数据库连接配置
# 3. 运行: docker-compose up -d
# 
# 环境变量说明:
# DB_HOST: 数据库主机地址 (容器环境中通常是服务名)
# DB_PORT: 数据库端口 (默认3306)
# DB_USERNAME: 数据库用户名 (默认root)
# DB_PASSWORD: 数据库密码 (通过环境变量MYSQL_ROOT_PASSWORD设置)