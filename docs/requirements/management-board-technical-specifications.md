# 经营调度会看板技术规范补充文档

## 📋 技术规范总览

基于现状评估和差距分析，补充详细的技术实现规范。

## 🗄️ 数据库查询规范

### 1. ImpairmentReserveRepository 扩展规范

#### 1.1 期初存量债权查询
```java
@Query("SELECT COALESCE(SUM(i.本月末债权余额), 0) FROM ImpairmentReserve i " +
       "WHERE i.id.year = :year AND i.id.month = :month " +
       "AND (i.期间 IS NULL OR i.期间 != :excludePeriod)")
BigDecimal findStockDebtBeginAmount(@Param("year") String year,
                                   @Param("month") String month,
                                   @Param("excludePeriod") String excludePeriod);

// 调用示例
BigDecimal stockDebtBegin = repository.findStockDebtBeginAmount("2024", "12", "2025年新增债权");
```

#### 1.2 累计处置金额查询
```java
@Query("SELECT COALESCE(SUM(i.本月处置债权), 0) FROM ImpairmentReserve i " +
       "WHERE i.id.year = :year AND CAST(i.id.month AS integer) <= :monthInt")
BigDecimal findTotalDisposalAmount(@Param("year") String year,
                                  @Param("monthInt") Integer monthInt);
```

#### 1.3 管理公司汇总查询
```java
@Query("SELECT i.管理公司, " +
       "COALESCE(SUM(i.本月末债权余额), 0) as stockAmount, " +
       "COALESCE(SUM(i.本月处置债权), 0) as disposalAmount, " +
       "COUNT(*) as recordCount " +
       "FROM ImpairmentReserve i " +
       "WHERE i.id.year = :year AND i.id.month = :month " +
       "AND (i.期间 IS NULL OR i.期间 != :excludePeriod) " +
       "GROUP BY i.管理公司 " +
       "ORDER BY stockAmount DESC")
List<Object[]> findStockDebtCompanySummary(@Param("year") String year,
                                          @Param("month") String month,
                                          @Param("excludePeriod") String excludePeriod);
```

#### 1.4 债权明细查询（支持筛选）
```java
@Query("SELECT i.管理公司, i.id.creditor, i.id.debtor, " +
       "i.本月末债权余额, i.本月处置债权, i.案件名称 " +
       "FROM ImpairmentReserve i " +
       "WHERE i.id.year = :year AND i.id.month = :month " +
       "AND (i.期间 IS NULL OR i.期间 != :excludePeriod) " +
       "AND i.本月末债权余额 > :minAmount " +
       "ORDER BY i.本月末债权余额 DESC")
List<Object[]> findStockDebtDetails(@Param("year") String year,
                                   @Param("month") String month,
                                   @Param("excludePeriod") String excludePeriod,
                                   @Param("minAmount") BigDecimal minAmount);
```

### 2. DisposalRepository 扩展规范

#### 2.1 处置方式统计查询
```java
@Query("SELECT " +
       "COALESCE(SUM(d.现金处置), 0) as cashDisposal, " +
       "COALESCE(SUM(d.分期还款), 0) as installmentPayment, " +
       "COALESCE(SUM(d.资产抵债), 0) as assetDebt, " +
       "COALESCE(SUM(d.其他方式), 0) as otherWays, " +
       "COALESCE(SUM(d.现金处置 + d.分期还款 + d.资产抵债 + d.其他方式), 0) as totalDisposal " +
       "FROM Disposal d " +
       "WHERE d.年份 = :year AND CAST(d.月份 AS integer) <= :monthInt")
Object[] findDisposalMethodsSummary(@Param("year") String year,
                                   @Param("monthInt") Integer monthInt);
```

#### 2.2 处置明细查询
```java
@Query("SELECT d.管理公司, d.债权人, d.债务人, " +
       "d.现金处置, d.分期还款, d.资产抵债, d.其他方式, " +
       "(d.现金处置 + d.分期还款 + d.资产抵债 + d.其他方式) as totalDisposal " +
       "FROM Disposal d " +
       "WHERE d.年份 = :year AND d.月份 = :month " +
       "AND (d.现金处置 + d.分期还款 + d.资产抵债 + d.其他方式) > :minAmount " +
       "ORDER BY totalDisposal DESC")
List<Object[]> findDisposalDetails(@Param("year") String year,
                                  @Param("month") String month,
                                  @Param("minAmount") BigDecimal minAmount);
```

### 3. NewDebtRepository 扩展规范

#### 3.1 新增债权累计数据查询
```java
@Query("SELECT n.管理公司, n.债权人, n.债务人, " +
       "SUM(CASE WHEN CAST(n.月份 AS integer) <= :monthInt THEN n.新增金额 ELSE 0 END) as cumulativeNew, " +
       "SUM(CASE WHEN CAST(n.月份 AS integer) <= :monthInt THEN n.处置金额 ELSE 0 END) as cumulativeDisposal, " +
       "(SUM(CASE WHEN CAST(n.月份 AS integer) <= :monthInt THEN n.新增金额 ELSE 0 END) - " +
       " SUM(CASE WHEN CAST(n.月份 AS integer) <= :monthInt THEN n.处置金额 ELSE 0 END)) as remainingBalance " +
       "FROM NewDebt n " +
       "WHERE n.年份 = :year " +
       "GROUP BY n.管理公司, n.债权人, n.债务人" +
       "HAVING cumulativeNew > 0 " +
       "ORDER BY cumulativeNew DESC")
List<Object[]> findCumulativeNewDebtData(@Param("year") String year,
                                        @Param("monthInt") Integer monthInt);
```

## 🔧 核心算法规范

### 1. 处置金额拆分算法

#### 1.1 算法接口定义
```java
@Component
public class DebtDisposalSplitter {
    
    /**
     * 拆分处置金额到新增债权和存量债权
     * 
     * @param disposalAmount 总处置金额
     * @param newDebtAmount 当年新增债权金额
     * @return 拆分结果
     */
    public DebtDisposalResult splitDisposal(BigDecimal disposalAmount, BigDecimal newDebtAmount) {
        // 参数验证
        if (disposalAmount == null || disposalAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("处置金额不能为空或负数");
        }
        if (newDebtAmount == null || newDebtAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("新增债权金额不能为空或负数");
        }
        
        DebtDisposalResult result = new DebtDisposalResult();
        
        if (disposalAmount.compareTo(newDebtAmount) <= 0) {
            // 情况1: 处置金额 <= 新增金额，全部归为新增债权处置
            result.setNewDebtDisposal(disposalAmount);
            result.setStockDebtDisposal(BigDecimal.ZERO);
            result.setSplitReason("处置金额未超出新增金额");
        } else {
            // 情况2: 处置金额 > 新增金额，需要拆分
            result.setNewDebtDisposal(newDebtAmount);
            result.setStockDebtDisposal(disposalAmount.subtract(newDebtAmount));
            result.setSplitReason("处置金额超出新增金额，超出部分归为存量处置");
        }
        
        // 设置原始数据
        result.setOriginalDisposal(disposalAmount);
        result.setOriginalNewDebt(newDebtAmount);
        
        return result;
    }
}

/**
 * 处置金额拆分结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DebtDisposalResult {
    private BigDecimal originalDisposal;    // 原始处置金额
    private BigDecimal originalNewDebt;     // 原始新增金额
    private BigDecimal newDebtDisposal;     // 新增债权处置金额
    private BigDecimal stockDebtDisposal;   // 存量债权处置金额
    private String splitReason;             // 拆分原因说明
    
    /**
     * 验证拆分结果的正确性
     */
    public boolean isValid() {
        BigDecimal total = newDebtDisposal.add(stockDebtDisposal);
        return total.compareTo(originalDisposal) == 0;
    }
}
```

#### 1.2 批量拆分处理
```java
/**
 * 批量处理债权处置拆分
 */
public List<DebtDisposalResult> batchSplitDisposal(List<DebtDisposalData> dataList) {
    return dataList.stream()
        .map(data -> splitDisposal(data.getDisposalAmount(), data.getNewDebtAmount()))
        .collect(Collectors.toList());
}
```

### 2. 前80%筛选算法

#### 2.1 通用筛选算法
```java
@Component
public class Top80PercentFilter {
    
    /**
     * 筛选前80%数据
     * 
     * @param dataList 已排序的数据列表
     * @param amountExtractor 金额提取函数
     * @param minAmount 最小金额阈值（万元）
     * @return 筛选后的数据列表
     */
    public <T> List<T> filterTop80Percent(List<T> dataList, 
                                         Function<T, BigDecimal> amountExtractor,
                                         BigDecimal minAmount) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 1. 计算80%的记录数量
        int totalCount = dataList.size();
        int top80Count = (int) Math.ceil(totalCount * 0.8);
        
        // 2. 取前80%的记录
        List<T> top80Percent = dataList.subList(0, Math.min(top80Count, totalCount));
        
        // 3. 再筛选金额大于阈值的记录
        List<T> filtered = top80Percent.stream()
            .filter(item -> {
                BigDecimal amount = amountExtractor.apply(item);
                return amount != null && amount.compareTo(minAmount) > 0;
            })
            .collect(Collectors.toList());
        
        return filtered;
    }
    
    /**
     * 专用于债权明细筛选
     */
    public List<DebtDetailDTO> filterDebtDetails(List<DebtDetailDTO> debtList) {
        // 按金额降序排序
        List<DebtDetailDTO> sortedList = debtList.stream()
            .sorted((a, b) -> b.getAmount().compareTo(a.getAmount()))
            .collect(Collectors.toList());
        
        // 筛选前80%且金额>100万
        return filterTop80Percent(sortedList, 
                                 DebtDetailDTO::getAmount, 
                                 new BigDecimal("100"));
    }
}
```

#### 2.2 分组筛选算法
```java
/**
 * 按管理公司分组筛选前80%
 */
public Map<String, List<DebtDetailDTO>> filterTop80PercentByCompany(List<DebtDetailDTO> debtList) {
    return debtList.stream()
        .collect(Collectors.groupingBy(DebtDetailDTO::getCompany))
        .entrySet().stream()
        .collect(Collectors.toMap(
            Map.Entry::getKey,
            entry -> filterDebtDetails(entry.getValue())
        ));
}
```

## 📊 Excel填充规范

### 1. 位置映射配置

#### 1.1 固定位置映射表
```java
@Configuration
public class ExcelPositionMapping {
    
    // 汇总数据位置映射
    public static final Map<String, CellPosition> SUMMARY_POSITIONS = Map.of(
        "期初存量债权", new CellPosition(5, 1),      // B6
        "本年累计新增", new CellPosition(8, 1),      // B9
        "存量债权累计处置", new CellPosition(5, 3),   // D6
        "本月清收金额", new CellPosition(5, 2),      // C6
        "分期还款", new CellPosition(12, 1),         // B13
        "现金处置", new CellPosition(13, 1),         // B14
        "资产抵债其他", new CellPosition(16, 1)      // B17
    );
    
    // 公司汇总数据位置映射
    public static final CellPosition STOCK_COMPANY_START = new CellPosition(21, 1);  // B22
    public static final CellPosition NEW_COMPANY_START = new CellPosition(21, 8);    // I22
    
    // 明细数据位置映射
    public static final CellPosition STOCK_DETAIL_START = new CellPosition(4, 13);   // N5
    public static final CellPosition NEW_DISPOSAL_START = new CellPosition(4, 17);   // R5
    public static final CellPosition NEW_DETAIL_START = new CellPosition(14, 13);    // N15
    public static final CellPosition NEW_BALANCE_START = new CellPosition(14, 17);   // R15
    
    @Data
    @AllArgsConstructor
    public static class CellPosition {
        private int row;    // 行索引（从0开始）
        private int column; // 列索引（从0开始）
        
        /**
         * 转换为Excel字母-数字表示法
         */
        public String toExcelNotation() {
            return getColumnLetter(column) + (row + 1);
        }
        
        private String getColumnLetter(int column) {
            StringBuilder result = new StringBuilder();
            while (column >= 0) {
                result.insert(0, (char) ('A' + column % 26));
                column = column / 26 - 1;
            }
            return result.toString();
        }
    }
}
```

### 2. 精确填充实现

#### 2.1 汇总数据填充
```java
@Service
public class ManagementBoardExcelFiller {
    
    @Autowired
    private ImpairmentReserveRepository impairmentReserveRepository;
    
    @Autowired
    private DisposalRepository disposalRepository;
    
    @Autowired
    private DebtDisposalSplitter debtDisposalSplitter;
    
    /**
     * 填充汇总数据到Excel固定位置
     */
    public void fillSummaryData(Cells cells, String year, String month) {
        try {
            // 1. B6: 期初存量债权
            BigDecimal stockDebtBegin = impairmentReserveRepository
                .findStockDebtBeginAmount("2024", "12", "2025年新增债权");
            setCellValue(cells, SUMMARY_POSITIONS.get("期初存量债权"), 
                        stockDebtBegin.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // 2. B9: 本年累计新增
            BigDecimal yearCumulativeNew = calculateYearCumulativeNew(year, month);
            setCellValue(cells, SUMMARY_POSITIONS.get("本年累计新增"), 
                        yearCumulativeNew.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // 3. D6: 存量债权本年累计处置
            BigDecimal stockDebtDisposal = calculateStockDebtDisposal(year, month);
            setCellValue(cells, SUMMARY_POSITIONS.get("存量债权累计处置"), 
                        stockDebtDisposal.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // 4. C6: 本月清收金额
            BigDecimal monthCollection = calculateMonthCollection(year, month);
            setCellValue(cells, SUMMARY_POSITIONS.get("本月清收金额"), 
                        monthCollection.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // 5. 处置方式统计
            fillDisposalMethodsSummary(cells, year, month);
            
        } catch (Exception e) {
            logger.error("填充汇总数据失败", e);
            throw new ExcelExportException("汇总数据填充失败: " + e.getMessage());
        }
    }
    
    /**
     * 填充处置方式统计
     */
    private void fillDisposalMethodsSummary(Cells cells, String year, String month) {
        Object[] disposalMethods = disposalRepository.findDisposalMethodsSummary(year, Integer.parseInt(month));
        if (disposalMethods != null && disposalMethods.length >= 5) {
            // B14: 现金处置
            setCellValue(cells, SUMMARY_POSITIONS.get("现金处置"), 
                        toBigDecimal(disposalMethods[0]).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // B13: 分期还款
            setCellValue(cells, SUMMARY_POSITIONS.get("分期还款"), 
                        toBigDecimal(disposalMethods[1]).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // B17: 资产抵债+其他
            BigDecimal assetAndOther = toBigDecimal(disposalMethods[2]).add(toBigDecimal(disposalMethods[3]));
            setCellValue(cells, SUMMARY_POSITIONS.get("资产抵债其他"), 
                        assetAndOther.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
        }
    }
    
    /**
     * 安全地设置单元格值
     */
    private void setCellValue(Cells cells, CellPosition position, Object value) {
        try {
            Cell cell = cells.get(position.getRow(), position.getColumn());
            if (value instanceof BigDecimal) {
                cell.setValue(((BigDecimal) value).doubleValue());
            } else if (value instanceof Number) {
                cell.setValue(((Number) value).doubleValue());
            } else if (value != null) {
                cell.setValue(value.toString());
            } else {
                cell.setValue("");
            }
        } catch (Exception e) {
            logger.warn("设置单元格值失败 [{}]: {}", position.toExcelNotation(), e.getMessage());
        }
    }
}
```

#### 2.2 动态数据填充
```java
/**
 * 填充管理公司汇总数据
 */
public void fillCompanySummaryData(Cells cells, String year, String month) {
    // 存量债权公司汇总 (B22开始)
    List<Object[]> stockCompanySummary = impairmentReserveRepository
        .findStockDebtCompanySummary(year, month, "2025年新增债权");
    fillCompanySummaryTable(cells, stockCompanySummary, STOCK_COMPANY_START, 
                           Arrays.asList("公司名称", "期初余额", "累计处置"));
    
    // 新增债权公司汇总 (I22开始)
    List<CompanySummaryDTO> newCompanySummary = calculateNewDebtCompanySummary(year, month);
    fillNewDebtCompanySummary(cells, newCompanySummary, NEW_COMPANY_START);
}

/**
 * 填充公司汇总表格
 */
private void fillCompanySummaryTable(Cells cells, List<Object[]> summaryData, 
                                   CellPosition startPosition, List<String> columnHeaders) {
    int currentRow = startPosition.getRow();
    
    // 填充数据行
    for (Object[] row : summaryData) {
        if (currentRow - startPosition.getRow() >= 10) {
            break; // 限制显示行数
        }
        
        for (int col = 0; col < row.length && col < columnHeaders.size(); col++) {
            CellPosition cellPos = new CellPosition(currentRow, startPosition.getColumn() + col);
            
            if (row[col] instanceof BigDecimal) {
                // 金额类数据转换为万元
                BigDecimal amount = (BigDecimal) row[col];
                setCellValue(cells, cellPos, amount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            } else {
                setCellValue(cells, cellPos, row[col]);
            }
        }
        currentRow++;
    }
}
```

## 🔍 数据验证规范

### 1. 数据一致性验证
```java
@Component
public class DataConsistencyValidator {
    
    /**
     * 验证处置金额拆分的一致性
     */
    public ValidationResult validateDisposalSplit(String year, String month) {
        ValidationResult result = new ValidationResult();
        
        try {
            // 1. 查询减值准备表累计处置金额
            BigDecimal impairmentDisposal = impairmentReserveRepository
                .findTotalDisposalAmount(year, Integer.parseInt(month));
            
            // 2. 查询处置表累计处置金额
            Object[] disposalSummary = disposalRepository
                .findDisposalMethodsSummary(year, Integer.parseInt(month));
            BigDecimal disposalTableTotal = toBigDecimal(disposalSummary[4]); // 总处置金额
            
            // 3. 比较两个金额的差异
            BigDecimal difference = impairmentDisposal.subtract(disposalTableTotal).abs();
            BigDecimal tolerance = new BigDecimal("1000"); // 1000元容差
            
            if (difference.compareTo(tolerance) > 0) {
                result.addWarning("减值准备表与处置表的处置金额存在差异: " + 
                                difference.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP) + "万元");
            }
            
            result.setValid(difference.compareTo(tolerance) <= 0);
            
        } catch (Exception e) {
            result.addError("数据一致性验证失败: " + e.getMessage());
            result.setValid(false);
        }
        
        return result;
    }
    
    @Data
    public static class ValidationResult {
        private boolean valid = true;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        
        public void addError(String error) {
            errors.add(error);
            valid = false;
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
        }
    }
}
```

## 📈 性能优化规范

### 1. 查询优化
```java
/**
 * 批量查询优化
 */
@Service
public class OptimizedDataQueryService {
    
    /**
     * 批量获取看板所需的所有数据
     */
    public ManagementBoardDataBundle getManagementBoardData(String year, String month) {
        ManagementBoardDataBundle bundle = new ManagementBoardDataBundle();
        
        // 并行执行多个查询
        CompletableFuture<BigDecimal> stockDebtFuture = CompletableFuture
            .supplyAsync(() -> impairmentReserveRepository.findStockDebtBeginAmount("2024", "12", "2025年新增债权"));
        
        CompletableFuture<Object[]> disposalMethodsFuture = CompletableFuture
            .supplyAsync(() -> disposalRepository.findDisposalMethodsSummary(year, Integer.parseInt(month)));
        
        CompletableFuture<List<Object[]>> stockCompanyFuture = CompletableFuture
            .supplyAsync(() -> impairmentReserveRepository.findStockDebtCompanySummary(year, month, "2025年新增债权"));
        
        // 等待所有查询完成
        CompletableFuture.allOf(stockDebtFuture, disposalMethodsFuture, stockCompanyFuture).join();
        
        // 收集结果
        try {
            bundle.setStockDebtBegin(stockDebtFuture.get());
            bundle.setDisposalMethods(disposalMethodsFuture.get());
            bundle.setStockCompanySummary(stockCompanyFuture.get());
        } catch (Exception e) {
            throw new RuntimeException("并行查询失败", e);
        }
        
        return bundle;
    }
}
```

### 2. 缓存策略
```java
@Service
@CacheConfig(cacheNames = "managementBoard")
public class CachedManagementBoardService {
    
    /**
     * 缓存基础数据查询（缓存1小时）
     */
    @Cacheable(key = "#year + '_' + #month", unless = "#result == null")
    public ManagementBoardBaseData getBaseData(String year, String month) {
        // 执行复杂查询
        return queryBaseData(year, month);
    }
    
    /**
     * 缓存失效策略
     */
    @CacheEvict(allEntries = true)
    public void clearCache() {
        logger.info("清空经营调度会看板缓存");
    }
}
```

---

**说明**: 本技术规范补充了现状评估中发现的所有技术实现细节，为后续开发提供了详细的指导。所有代码示例都基于实际的数据库表结构和现有系统架构。