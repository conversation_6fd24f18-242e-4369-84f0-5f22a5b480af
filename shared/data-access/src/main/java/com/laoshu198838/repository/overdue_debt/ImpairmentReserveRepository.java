package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve.ImpairmentReserveKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 减值准备表数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface ImpairmentReserveRepository extends JpaRepository<ImpairmentReserve, ImpairmentReserveKey> {

    /**
     * 批量更新"本年度累计回收"字段
     *
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query(value = """
                   UPDATE 减值准备表 t1
                   JOIN (
                       SELECT
                           债权人,
                           债务人,
                           期间,
                           年份,
                           月份,
                           是否涉诉,
                           SUM(本月处置债权) OVER (
                               PARTITION BY 债权人, 债务人, 期间, 年份, 是否涉诉
                               ORDER BY 月份
                               ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                           ) AS 累计处置
                       FROM 减值准备表
                   ) t2
                   ON t1.债权人 = t2.债权人
                      AND t1.债务人 = t2.债务人
                      AND t1.期间 = t2.期间
                      AND t1.年份 = t2.年份
                      AND t1.月份 = t2.月份
                      AND t1.是否涉诉 = t2.是否涉诉
                   SET t1.本年度累计回收 = t2.累计处置
                   WHERE t1.本年度累计回收 <> t2.累计处置
                     AND t2.累计处置 IS NOT NULL
                   """, nativeQuery = true)
    int updateAnnualCumulativeRecovery();

    /**
     * 根据年份和月份查询减值准备表中的新增债权总额
     *
     * @param year  年份
     * @param month 月份
     * @return 新增债权总额
     */
    @Query("SELECT COALESCE(SUM(i.currentMonthNewDebt), 0) FROM ImpairmentReserve i WHERE i.id.year = :year AND i.id.month = :month")
    BigDecimal sumCurrentMonthNewDebtByYearAndMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 根据年份和月份查询减值准备表中的数据
     *
     * @param year  年份
     * @param month 月份
     * @return 减值准备表记录列表
     */
    @Query("SELECT i FROM ImpairmentReserve i WHERE i.id.year = :year AND i.id.month = :month")
    List<ImpairmentReserve> findByIdYearAndIdMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 根据债权人和债务人查询减值准备表记录
     *
     * @param creditor 债权人
     * @param debtor   债务人
     * @return 匹配的减值准备表记录列表，按年月降序排序
     */
    @Query("SELECT i FROM ImpairmentReserve i WHERE i.id.creditor = :creditor AND i.id.debtor = :debtor ORDER BY i.id.year DESC, i.id.month DESC")
    List<ImpairmentReserve> findByIdCreditorAndIdDebtor(@Param("creditor") String creditor, @Param("debtor") String debtor);

    /**
     * 根据年份查询减值准备表处置记录
     *
     * @param year 年份
     * @return 匹配的减值准备表记录列表
     */
    @Query("SELECT i FROM ImpairmentReserve i " +
           "WHERE i.id.year = :year " +
           "  AND i.currentMonthDisposeDebt <> 0 " +
           "ORDER BY i.id.month ASC, i.currentMonthDisposeDebt DESC")
    List<ImpairmentReserve> findDecreaseByYear(@Param("year") int year);

    /**
     * 根据年份查询减值准备表新增记录
     *
     * @param year 年份
     * @return 匹配的减值准备表记录列表
     */
    @Query("SELECT i FROM ImpairmentReserve i " +
           "WHERE i.id.year = :year " +
           "  AND i.currentMonthNewDebt <> 0 " +
           "ORDER BY i.id.month ASC, i.currentMonthNewDebt DESC")
    List<ImpairmentReserve> findAddByYear(@Param("year") int year);

    // ==================== 存量债权清收情况统计查询方法 ====================

    /**
     * 获取期初金额：根据传入年份搜索上年12月份的本月末债权余额之和
     * 排除2025年新增债权
     *
     * @param year 当前年份
     * @param company 管理公司（全部或具体公司名）
     * @return 期初金额
     */
    @Query(value = """
        SELECT COALESCE(SUM(本月末债权余额), 0) 
        FROM 减值准备表 
        WHERE 年份 = :previousYear 
          AND 月份 = 12 
          AND (:company IN ('全部', '所有公司') OR 管理公司 = :company)
          AND 期间 != '2025年新增债权'
    """, nativeQuery = true)
    BigDecimal findYearBeginAmount(@Param("previousYear") int previousYear, @Param("company") String company);

    /**
     * 获取本月清收金额：根据传入的年份和月份筛选本月处置债权之和
     * 排除2025年新增债权
     *
     * @param year 年份
     * @param month 月份
     * @param company 管理公司（全部或具体公司名）
     * @return 本月清收金额
     */
    @Query(value = """
        SELECT COALESCE(SUM(本月处置债权), 0) 
        FROM 减值准备表 
        WHERE 年份 = :year 
          AND 月份 = :month 
          AND (:company IN ('全部', '所有公司') OR 管理公司 = :company)
          AND 期间 != '2025年新增债权'
    """, nativeQuery = true)
    BigDecimal findMonthCollectionAmount(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    /**
     * 获取本年累计清收金额：根据传入的年份和月份筛选累加到本月的本月处置债权之和
     * 排除2025年新增债权
     *
     * @param year 年份
     * @param month 月份（累计到该月份）
     * @param company 管理公司（全部或具体公司名）
     * @return 本年累计清收金额
     */
    @Query(value = """
        SELECT COALESCE(SUM(本月处置债权), 0) 
        FROM 减值准备表 
        WHERE 年份 = :year 
          AND 月份 <= :month 
          AND (:company IN ('全部', '所有公司') OR 管理公司 = :company)
          AND 期间 != '2025年新增债权'
    """, nativeQuery = true)
    BigDecimal findYearCumulativeCollectionAmount(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    /**
     * 获取期末余额：根据传入的年份和月份从减值准备表中筛选本月末债权余额之和
     * 排除2025年新增债权
     *
     * @param year 年份
     * @param month 月份
     * @param company 管理公司（全部或具体公司名）
     * @return 期末余额
     */
    @Query(value = """
        SELECT COALESCE(SUM(本月末债权余额), 0) 
        FROM 减值准备表 
        WHERE 年份 = :year 
          AND 月份 = :month 
          AND (:company IN ('全部', '所有公司') OR 管理公司 = :company)
          AND 期间 != '2025年新增债权'
    """, nativeQuery = true)
    BigDecimal findPeriodEndAmount(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    // ==================== 存量债权清收情况明细查询方法 ====================

    /**
     * 获取期初金额明细数据
     * 排除2025年新增债权
     */
    @Query(value = """
        SELECT 债权人, 债务人, 管理公司, 是否涉诉, 期间, 本月末债权余额 as amount
        FROM 减值准备表 
        WHERE 年份 = :previousYear 
          AND 月份 = 12 
          AND (:company IN ('全部', '所有公司') OR 管理公司 = :company)
          AND 期间 != '2025年新增债权'
          AND 本月末债权余额 > 0
        ORDER BY 本月末债权余额 DESC
    """, nativeQuery = true)
    List<Object[]> findYearBeginAmountDetails(@Param("previousYear") int previousYear, @Param("company") String company);

    /**
     * 获取本月清收金额明细数据
     * 排除2025年新增债权
     */
    @Query(value = """
        SELECT 债权人, 债务人, 管理公司, 是否涉诉, 期间, 本月处置债权 as amount
        FROM 减值准备表 
        WHERE 年份 = :year 
          AND 月份 = :month 
          AND (:company IN ('全部', '所有公司') OR 管理公司 = :company)
          AND 期间 != '2025年新增债权'
          AND 本月处置债权 > 0
        ORDER BY 本月处置债权 DESC
    """, nativeQuery = true)
    List<Object[]> findMonthCollectionAmountDetails(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    /**
     * 获取本年累计清收金额明细数据
     * 排除2025年新增债权
     */
    @Query(value = """
        SELECT 债权人, 债务人, 管理公司, 是否涉诉, 期间, SUM(本月处置债权) as amount
        FROM 减值准备表 
        WHERE 年份 = :year 
          AND 月份 <= :month 
          AND (:company IN ('全部', '所有公司') OR 管理公司 = :company)
          AND 期间 != '2025年新增债权'
          AND 本月处置债权 > 0
        GROUP BY 债权人, 债务人, 管理公司, 是否涉诉, 期间
        ORDER BY amount DESC
    """, nativeQuery = true)
    List<Object[]> findYearCumulativeCollectionAmountDetails(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    /**
     * 获取期末余额明细数据
     * 排除2025年新增债权
     */
    @Query(value = """
        SELECT 债权人, 债务人, 管理公司, 是否涉诉, 期间, 本月末债权余额 as amount
        FROM 减值准备表 
        WHERE 年份 = :year 
          AND 月份 = :month 
          AND (:company IN ('全部', '所有公司') OR 管理公司 = :company)
          AND 期间 != '2025年新增债权'
          AND 本月末债权余额 > 0
        ORDER BY 本月末债权余额 DESC
    """, nativeQuery = true)
    List<Object[]> findPeriodEndAmountDetails(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    // ==================== 经营调度会看板专用查询方法 ====================

    /**
     * 期初存量债权查询（经营调度会看板）
     * 查询指定年份期初的存量债权金额，排除指定期间的新增债权
     *
     * @param year 年份（字符串）
     * @param month 月份（字符串）
     * @param excludePeriod 需要排除的期间（如：2025年新增债权）
     * @return 期初存量债权金额
     */
    @Query("SELECT COALESCE(SUM(i.本月末债权余额), 0) FROM ImpairmentReserve i " +
           "WHERE i.id.year = :year AND i.id.month = :month " +
           "AND (i.期间 IS NULL OR i.期间 != :excludePeriod)")
    BigDecimal findStockDebtBeginAmount(@Param("year") String year,
                                       @Param("month") String month,
                                       @Param("excludePeriod") String excludePeriod);

    /**
     * 累计处置金额查询（经营调度会看板）
     * 查询指定年份从1月累计到指定月份的总处置金额
     *
     * @param year 年份（字符串）
     * @param monthInt 月份（整数，累计到该月份）
     * @return 累计处置金额
     */
    @Query("SELECT COALESCE(SUM(i.本月处置债权), 0) FROM ImpairmentReserve i " +
           "WHERE i.id.year = :year AND CAST(i.id.month AS integer) <= :monthInt")
    BigDecimal findTotalDisposalAmount(@Param("year") String year,
                                      @Param("monthInt") Integer monthInt);

    /**
     * 存量债权公司汇总查询（经营调度会看板）
     * 按管理公司汇总存量债权的余额和处置情况，排除指定期间的新增债权
     *
     * @param year 年份（字符串）
     * @param month 月份（字符串）
     * @param excludePeriod 需要排除的期间（如：2025年新增债权）
     * @return 公司汇总数据 [管理公司, 存量金额, 处置金额, 记录数]
     */
    @Query("SELECT i.管理公司, " +
           "COALESCE(SUM(i.本月末债权余额), 0) as stockAmount, " +
           "COALESCE(SUM(i.本月处置债权), 0) as disposalAmount, " +
           "COUNT(*) as recordCount " +
           "FROM ImpairmentReserve i " +
           "WHERE i.id.year = :year AND i.id.month = :month " +
           "AND (i.期间 IS NULL OR i.期间 != :excludePeriod) " +
           "GROUP BY i.管理公司 " +
           "ORDER BY stockAmount DESC")
    List<Object[]> findStockDebtCompanySummary(@Param("year") String year,
                                              @Param("month") String month,
                                              @Param("excludePeriod") String excludePeriod);

    /**
     * 债权明细查询支持筛选（经营调度会看板）
     * 查询债权明细数据，支持最小金额筛选，排除指定期间的新增债权
     *
     * @param year 年份（字符串）
     * @param month 月份（字符串）
     * @param excludePeriod 需要排除的期间（如：2025年新增债权）
     * @param minAmount 最小金额阈值
     * @return 债权明细数据 [管理公司, 债权人, 债务人, 本月末债权余额, 本月处置债权, 案件名称]
     */
    @Query("SELECT i.管理公司, i.id.creditor, i.id.debtor, " +
           "i.本月末债权余额, i.本月处置债权, i.案件名称 " +
           "FROM ImpairmentReserve i " +
           "WHERE i.id.year = :year AND i.id.month = :month " +
           "AND (i.期间 IS NULL OR i.期间 != :excludePeriod) " +
           "AND i.本月末债权余额 > :minAmount " +
           "ORDER BY i.本月末债权余额 DESC")
    List<Object[]> findStockDebtDetails(@Param("year") String year,
                                       @Param("month") String month,
                                       @Param("excludePeriod") String excludePeriod,
                                       @Param("minAmount") BigDecimal minAmount);
}
