#!/bin/bash

###############################################################################
# 财务管理系统 - 全自动化部署脚本
# 功能：无交互式部署前后端到Linux服务器
###############################################################################

# 配置部分
SERVER_IP="**********"
SERVER_USER="root"
REMOTE_BACKEND_PATH="/opt/financial-system"
REMOTE_FRONTEND_PATH="/var/www/financial-system"
LOCAL_PROJECT_PATH=$(pwd)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查SSH连接
check_ssh_connection() {
    log_info "检查SSH连接到 $SERVER_IP..."
    ssh -o ConnectTimeout=5 $SERVER_USER@$SERVER_IP "echo 'SSH连接成功'" > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        log_error "无法连接到服务器 $SERVER_IP"
    fi
    log_info "SSH连接正常"
}

# 构建前端
build_frontend() {
    log_info "开始构建前端应用..."
    cd $LOCAL_PROJECT_PATH/FinancialSystem-web
    
    # 检查node_modules
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    # 构建
    log_info "执行前端构建..."
    npm run build
    
    if [ $? -ne 0 ]; then
        log_error "前端构建失败"
    fi
    
    log_info "前端构建完成"
    cd $LOCAL_PROJECT_PATH
}

# 构建后端
build_backend() {
    log_info "开始构建后端应用..."
    cd $LOCAL_PROJECT_PATH
    
    # Maven构建
    log_info "执行Maven构建..."
    mvn clean package -DskipTests
    
    if [ $? -ne 0 ]; then
        log_error "后端构建失败"
    fi
    
    log_info "后端构建完成"
}

# 部署前端
deploy_frontend() {
    log_info "开始部署前端..."
    
    # 压缩前端文件
    log_info "压缩前端构建文件..."
    cd $LOCAL_PROJECT_PATH/FinancialSystem-web
    tar -czf /tmp/frontend-build.tar.gz -C build .
    
    # 上传到服务器
    log_info "上传前端文件到服务器..."
    scp /tmp/frontend-build.tar.gz $SERVER_USER@$SERVER_IP:/tmp/
    
    # 在服务器上解压和配置
    log_info "在服务器上配置前端文件..."
    ssh $SERVER_USER@$SERVER_IP "
        # 备份现有文件
        if [ -d '$REMOTE_FRONTEND_PATH' ]; then
            cp -r $REMOTE_FRONTEND_PATH ${REMOTE_FRONTEND_PATH}.backup.\$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
        fi
        
        # 创建目录
        mkdir -p $REMOTE_FRONTEND_PATH
        
        # 解压文件
        cd $REMOTE_FRONTEND_PATH
        rm -rf *
        tar -xzf /tmp/frontend-build.tar.gz
        
        # 设置权限
        chown -R nginx:nginx $REMOTE_FRONTEND_PATH
        chcon -R -t httpd_sys_content_t $REMOTE_FRONTEND_PATH 2>/dev/null || true
        chmod -R 755 $REMOTE_FRONTEND_PATH
        
        # 重新加载nginx
        systemctl reload nginx
        
        echo '前端部署完成'
    "
    
    log_info "前端部署完成"
    cd $LOCAL_PROJECT_PATH
}

# 部署后端
deploy_backend() {
    log_info "开始部署后端..."
    
    # 压缩后端文件
    log_info "压缩后端JAR文件..."
    cd $LOCAL_PROJECT_PATH
    tar -czf /tmp/backend-app.tar.gz -C api-gateway/target api-gateway-1.0-SNAPSHOT.jar
    
    # 上传到服务器
    log_info "上传后端文件到服务器..."
    scp /tmp/backend-app.tar.gz $SERVER_USER@$SERVER_IP:/tmp/
    
    # 在服务器上解压和配置
    log_info "在服务器上配置后端文件..."
    ssh $SERVER_USER@$SERVER_IP "
        # 停止现有服务
        systemctl stop financial-backend 2>/dev/null || true
        pkill -f 'api-gateway' 2>/dev/null || true
        
        # 备份现有文件
        if [ -d '$REMOTE_BACKEND_PATH' ]; then
            cp -r $REMOTE_BACKEND_PATH ${REMOTE_BACKEND_PATH}.backup.\$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
        fi
        
        # 创建目录
        mkdir -p $REMOTE_BACKEND_PATH
        
        # 解压文件
        cd $REMOTE_BACKEND_PATH
        rm -f *.jar
        tar -xzf /tmp/backend-app.tar.gz
        
        # 设置权限
        chown -R root:root $REMOTE_BACKEND_PATH
        chmod +x $REMOTE_BACKEND_PATH/*.jar
        
        # 启动服务
        systemctl start financial-backend
        
        # 等待服务启动
        sleep 10
        
        # 检查服务状态
        if systemctl is-active financial-backend > /dev/null; then
            echo '后端服务启动成功'
        else
            echo '后端服务启动失败'
            systemctl status financial-backend --no-pager -l
            exit 1
        fi
    "
    
    log_info "后端部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    ssh $SERVER_USER@$SERVER_IP "
        echo '=== 服务状态 ==='
        systemctl is-active financial-backend
        systemctl is-active nginx
        
        echo ''
        echo '=== 端口检查 ==='
        netstat -tlnp | grep ':8080\|:80' || echo '端口检查失败'
        
        echo ''
        echo '=== 文件时间戳 ==='
        echo '前端文件:'
        ls -la $REMOTE_FRONTEND_PATH/ | head -5
        echo '后端文件:'
        ls -la $REMOTE_BACKEND_PATH/ | head -5
        
        echo ''
        echo '=== API测试 ==='
        curl -s http://localhost:8080/health | head -50 || echo 'API测试失败'
    "
    
    log_info "部署验证完成"
}

# 主函数
main() {
    log_info "========================================="
    log_info "财务管理系统全自动化部署开始"
    log_info "目标服务器: $SERVER_IP"
    log_info "========================================="
    
    # 检查连接
    check_ssh_connection
    
    # 构建
    build_frontend
    build_backend
    
    # 部署
    deploy_frontend
    deploy_backend
    
    # 验证
    verify_deployment
    
    log_info "========================================="
    log_info "全自动化部署完成！"
    log_info "前端地址: http://$SERVER_IP"
    log_info "后端API: http://$SERVER_IP:8080"
    log_info "========================================="
}

# 执行主函数
main "$@"