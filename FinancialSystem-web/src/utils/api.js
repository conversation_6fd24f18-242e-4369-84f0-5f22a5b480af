import axios from 'axios';

const api = axios.create({
  baseURL: '/api', // 使用相对路径，通过前端代理转发到后端
  headers: {
    'Content-Type': 'application/json'
  }
});

// 一个标志，用于防止重复刷新
let isRefreshing = false;
// 记录最后一次登录请求的时间
let lastLoginAttempt = 0;
// 记录失败的请求计数
let failedRequestsCount = 0;
// 最大失败请求数，超过此数目将强制重新登录
const MAX_FAILED_REQUESTS = 5;

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 如果是登录请求，记录时间并重置失败计数
    if (config.url.includes('/auth/login')) {
      lastLoginAttempt = Date.now();
      failedRequestsCount = 0;
      return config;
    }

    // 获取认证令牌 - 优先使用独立存储的 token
    const token = localStorage.getItem('token');

    if (token) {
      // 直接设置 Authorization 头
      config.headers.Authorization = `Bearer ${token}`;

      // 不再添加自定义请求头，避免可能的 CORS 问题
      // if (parsed.username) config.headers['X-User-Name'] = parsed.username;
      // if (parsed.company) config.headers['X-User-Company'] = parsed.company;
      // if (parsed.department) config.headers['X-User-Department'] = parsed.department;
    } else {
      // 如果没有找到 token，尝试从 auth 对象中获取
      const authData = localStorage.getItem('auth');
      if (authData) {
        try {
          const parsed = JSON.parse(authData);
          if (parsed && parsed.token) {
            config.headers.Authorization = `Bearer ${parsed.token}`;
          }
        } catch (e) {
          // 静默处理解析错误
        }
      }
    }

    // 生产环境中不输出请求日志

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 成功响应，重置失败计数
    failedRequestsCount = 0;
    return response;
  },
  error => {
    // 如果是登录请求，直接返回错误，不处理重定向
    const isLoginRequest =
      error.config && error.config.url && error.config.url.includes('/auth/login');

    // 获取当前时间
    const now = Date.now();
    // 如果是登录过程中（最后一次登录请求后10秒内），不触发重定向
    if (isLoginRequest || now - lastLoginAttempt < 10000) {
      return Promise.reject(error);
    }

    // 记录请求失败信息（用于调试）

    // 对 401/403 错误特殊处理
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      // 增加失败计数
      failedRequestsCount++;

      // 如果超过最大失败次数，进行重定向
      if (failedRequestsCount >= MAX_FAILED_REQUESTS) {
        // 防止多次触发刷新
        if (!isRefreshing) {
          isRefreshing = true;

          // 在非登录页面才重定向到登录页面
          const currentPath = window.location.pathname;
          if (!currentPath.includes('/authentication/sign-in')) {
            // 清除所有认证数据
            localStorage.removeItem('auth');
            localStorage.removeItem('token');
            localStorage.removeItem('user');

            // 添加警告消息
            const message =
              error.response.status === 401
                ? '您的登录已过期，请重新登录'
                : '您没有权限访问此资源，请重新登录';

            // 跳转到登录页面，并传递消息
            window.location.href = `/authentication/sign-in?message=${encodeURIComponent(message)}`;
          } else {
            // 如果已经在登录页面，只需刷新状态
            failedRequestsCount = 0;
          }

          // 5秒后重置刷新状态，以防止卡死
          setTimeout(() => {
            isRefreshing = false;
          }, 5000);
        }
      }
    }

    return Promise.reject(error);
  }
);

export default api;
