package com.laoshu198838.service;

import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDecreaseRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDetailRepository;
import com.laoshu198838.util.business.DebtDisposalSplitter;
import com.laoshu198838.util.business.Top80PercentFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 经营调度会看板导出服务单元测试
 * 验证新增的Repository查询方法和业务算法的正确性
 * 
 * <AUTHOR> Code
 * @since 2025-01-18
 */
@ExtendWith(MockitoExtension.class)
class ManagementBoardExportServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(ManagementBoardExportServiceTest.class);

    @Mock
    private ImpairmentReserveRepository impairmentReserveRepository;

    @Mock
    private OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;

    @Mock
    private OverdueDebtDetailRepository overdueDebtDetailRepository;

    @Mock
    private DebtDisposalSplitter debtDisposalSplitter;

    @Mock
    private Top80PercentFilter top80PercentFilter;

    @InjectMocks
    private ManagementBoardExportService managementBoardExportService;

    @BeforeEach
    void setUp() {
        logger.info("开始单元测试设置");
    }

    /**
     * 测试ImpairmentReserveRepository新增的查询方法
     */
    @Test
    void testImpairmentReserveRepositoryQueries() {
        logger.info("测试ImpairmentReserveRepository新增查询方法");

        // 1. 测试期初存量债权查询
        String year = "2025";
        String month = "12";
        String excludePeriod = "2025年新增债权";
        BigDecimal expectedStockDebt = new BigDecimal("50000000"); // 5000万

        when(impairmentReserveRepository.findStockDebtBeginAmount(year, month, excludePeriod))
            .thenReturn(expectedStockDebt);

        BigDecimal actualStockDebt = impairmentReserveRepository.findStockDebtBeginAmount(year, month, excludePeriod);
        assertEquals(expectedStockDebt, actualStockDebt, "期初存量债权查询结果不正确");

        // 2. 测试累计处置金额查询
        BigDecimal expectedTotalDisposal = new BigDecimal("10000000"); // 1000万

        when(impairmentReserveRepository.findTotalDisposalAmount(year, 12))
            .thenReturn(expectedTotalDisposal);

        BigDecimal actualTotalDisposal = impairmentReserveRepository.findTotalDisposalAmount(year, 12);
        assertEquals(expectedTotalDisposal, actualTotalDisposal, "累计处置金额查询结果不正确");

        // 3. 测试公司汇总查询
        Object[] company1 = {"公司A", new BigDecimal("20000000"), new BigDecimal("5000000"), 10L};
        Object[] company2 = {"公司B", new BigDecimal("30000000"), new BigDecimal("5000000"), 15L};
        List<Object[]> expectedCompanySummary = Arrays.asList(company1, company2);

        when(impairmentReserveRepository.findStockDebtCompanySummary(year, month, excludePeriod))
            .thenReturn(expectedCompanySummary);

        List<Object[]> actualCompanySummary = impairmentReserveRepository.findStockDebtCompanySummary(year, month, excludePeriod);
        assertEquals(expectedCompanySummary.size(), actualCompanySummary.size(), "公司汇总查询结果数量不正确");

        verify(impairmentReserveRepository).findStockDebtBeginAmount(year, month, excludePeriod);
        verify(impairmentReserveRepository).findTotalDisposalAmount(year, 12);
        verify(impairmentReserveRepository).findStockDebtCompanySummary(year, month, excludePeriod);

        logger.info("ImpairmentReserveRepository查询方法测试通过");
    }

    /**
     * 测试OverdueDebtDecreaseRepository新增的查询方法
     */
    @Test
    void testOverdueDebtDecreaseRepositoryQueries() {
        logger.info("测试OverdueDebtDecreaseRepository新增查询方法");

        String year = "2025";
        Integer monthInt = 12;

        // 1. 测试处置方式统计查询
        Object[] expectedDisposalMethods = {
            new BigDecimal("3000000"),  // 现金处置
            new BigDecimal("2000000"),  // 分期还款
            new BigDecimal("3000000"),  // 资产抵债
            new BigDecimal("2000000"),  // 其他方式
            new BigDecimal("10000000")  // 总处置金额
        };

        when(overdueDebtDecreaseRepository.findDisposalMethodsSummaryNative(year, monthInt))
            .thenReturn(expectedDisposalMethods);

        Object[] actualDisposalMethods = overdueDebtDecreaseRepository.findDisposalMethodsSummaryNative(year, monthInt);
        assertNotNull(actualDisposalMethods, "处置方式统计查询结果不应为空");
        assertEquals(5, actualDisposalMethods.length, "处置方式统计查询结果应包含5个元素");

        // 2. 测试处置明细查询
        BigDecimal minAmount = new BigDecimal("1000000"); // 100万
        Object[] disposal1 = {"公司A", "债权人1", "债务人1", new BigDecimal("2000000"), new BigDecimal("1000000"), 
                             new BigDecimal("0"), new BigDecimal("0"), new BigDecimal("3000000")};
        List<Object[]> expectedDisposalDetails = Collections.singletonList(disposal1);

        when(overdueDebtDecreaseRepository.findDisposalDetailsNative(year, "12", minAmount))
            .thenReturn(expectedDisposalDetails);

        List<Object[]> actualDisposalDetails = overdueDebtDecreaseRepository.findDisposalDetailsNative(year, "12", minAmount);
        assertEquals(expectedDisposalDetails.size(), actualDisposalDetails.size(), "处置明细查询结果数量不正确");

        verify(overdueDebtDecreaseRepository).findDisposalMethodsSummaryNative(year, monthInt);
        verify(overdueDebtDecreaseRepository).findDisposalDetailsNative(year, "12", minAmount);

        logger.info("OverdueDebtDecreaseRepository查询方法测试通过");
    }

    /**
     * 测试OverdueDebtDetailRepository新增的查询方法
     */
    @Test
    void testOverdueDebtDetailRepositoryQueries() {
        logger.info("测试OverdueDebtDetailRepository新增查询方法");

        String year = "2025";
        Integer monthInt = 12;

        // 1. 测试年度新增债权汇总查询
        BigDecimal expectedYearNewDebt = new BigDecimal("30000000"); // 3000万

        when(overdueDebtDetailRepository.findYearCumulativeNewDebtAmount(year, monthInt))
            .thenReturn(expectedYearNewDebt);

        BigDecimal actualYearNewDebt = overdueDebtDetailRepository.findYearCumulativeNewDebtAmount(year, monthInt);
        assertEquals(expectedYearNewDebt, actualYearNewDebt, "年度新增债权汇总查询结果不正确");

        // 2. 测试新增债权公司汇总查询
        Object[] newCompany1 = {"公司A", new BigDecimal("15000000"), new BigDecimal("5000000"), new BigDecimal("10000000"), 8L};
        Object[] newCompany2 = {"公司B", new BigDecimal("15000000"), new BigDecimal("3000000"), new BigDecimal("12000000"), 12L};
        List<Object[]> expectedNewCompanySummary = Arrays.asList(newCompany1, newCompany2);

        when(overdueDebtDetailRepository.findNewDebtCompanySummary(year, monthInt))
            .thenReturn(expectedNewCompanySummary);

        List<Object[]> actualNewCompanySummary = overdueDebtDetailRepository.findNewDebtCompanySummary(year, monthInt);
        assertEquals(expectedNewCompanySummary.size(), actualNewCompanySummary.size(), "新增债权公司汇总查询结果数量不正确");

        // 3. 测试新增债权明细查询
        BigDecimal minAmount = new BigDecimal("1000000"); // 100万
        Object[] newDebt1 = {"公司A", "债权人1", "债务人1", new BigDecimal("5000000"), new BigDecimal("3000000"), "案件1"};
        List<Object[]> expectedNewDebtDetails = Collections.singletonList(newDebt1);

        when(overdueDebtDetailRepository.findNewDebtDetails(year, monthInt, minAmount))
            .thenReturn(expectedNewDebtDetails);

        List<Object[]> actualNewDebtDetails = overdueDebtDetailRepository.findNewDebtDetails(year, monthInt, minAmount);
        assertEquals(expectedNewDebtDetails.size(), actualNewDebtDetails.size(), "新增债权明细查询结果数量不正确");

        verify(overdueDebtDetailRepository).findYearCumulativeNewDebtAmount(year, monthInt);
        verify(overdueDebtDetailRepository).findNewDebtCompanySummary(year, monthInt);
        verify(overdueDebtDetailRepository).findNewDebtDetails(year, monthInt, minAmount);

        logger.info("OverdueDebtDetailRepository查询方法测试通过");
    }

    /**
     * 测试处置金额拆分算法
     */
    @Test
    void testDebtDisposalSplitter() {
        logger.info("测试处置金额拆分算法");

        // 创建真实的DebtDisposalSplitter实例进行测试
        DebtDisposalSplitter realSplitter = new DebtDisposalSplitter();

        // 测试场景1：处置金额 <= 新增金额
        BigDecimal disposalAmount1 = new BigDecimal("8000000");  // 800万
        BigDecimal newDebtAmount1 = new BigDecimal("10000000"); // 1000万
        
        DebtDisposalSplitter.DebtDisposalResult result1 = realSplitter.splitDisposal(disposalAmount1, newDebtAmount1);
        
        assertEquals(disposalAmount1, result1.getNewDebtDisposal(), "新增债权处置金额应等于总处置金额");
        assertEquals(BigDecimal.ZERO, result1.getStockDebtDisposal(), "存量债权处置金额应为0");
        assertTrue(result1.isValid(), "拆分结果应该有效");

        // 测试场景2：处置金额 > 新增金额
        BigDecimal disposalAmount2 = new BigDecimal("15000000"); // 1500万
        BigDecimal newDebtAmount2 = new BigDecimal("10000000");  // 1000万
        
        DebtDisposalSplitter.DebtDisposalResult result2 = realSplitter.splitDisposal(disposalAmount2, newDebtAmount2);
        
        assertEquals(newDebtAmount2, result2.getNewDebtDisposal(), "新增债权处置金额应等于新增债权金额");
        assertEquals(new BigDecimal("5000000"), result2.getStockDebtDisposal(), "存量债权处置金额应为超出部分");
        assertTrue(result2.isValid(), "拆分结果应该有效");

        // 测试参数验证
        assertThrows(IllegalArgumentException.class, () -> {
            realSplitter.splitDisposal(null, newDebtAmount1);
        }, "处置金额为null应抛出异常");

        assertThrows(IllegalArgumentException.class, () -> {
            realSplitter.splitDisposal(new BigDecimal("-1000"), newDebtAmount1);
        }, "处置金额为负数应抛出异常");

        logger.info("处置金额拆分算法测试通过");
    }

    /**
     * 测试前80%筛选算法
     */
    @Test
    void testTop80PercentFilter() {
        logger.info("测试前80%筛选算法");

        // 创建真实的Top80PercentFilter实例进行测试
        Top80PercentFilter realFilter = new Top80PercentFilter();

        // 创建测试数据
        List<Top80PercentFilter.DebtDetailDTO> testData = Arrays.asList(
            new Top80PercentFilter.DebtDetailDTO("公司A", "债权人1", "债务人1", new BigDecimal("5000000")),
            new Top80PercentFilter.DebtDetailDTO("公司A", "债权人2", "债务人2", new BigDecimal("3000000")),
            new Top80PercentFilter.DebtDetailDTO("公司B", "债权人3", "债务人3", new BigDecimal("2000000")),
            new Top80PercentFilter.DebtDetailDTO("公司B", "债权人4", "债务人4", new BigDecimal("1500000")),
            new Top80PercentFilter.DebtDetailDTO("公司C", "债权人5", "债务人5", new BigDecimal("800000")) // 低于100万阈值
        );

        // 测试前80%筛选
        List<Top80PercentFilter.DebtDetailDTO> filteredData = realFilter.filterTop80Percent(
            testData, 
            Top80PercentFilter.DebtDetailDTO::getAmount,
            new BigDecimal("1000000") // 100万阈值
        );

        // 验证筛选结果
        assertTrue(filteredData.size() <= Math.ceil(testData.size() * 0.8), "筛选后的数据应不超过原数据的80%");
        
        // 验证所有筛选出的数据都大于阈值
        for (Top80PercentFilter.DebtDetailDTO debt : filteredData) {
            assertTrue(debt.getAmount().compareTo(new BigDecimal("1000000")) > 0, 
                      "筛选出的债权金额应大于100万");
        }

        // 测试统计信息计算
        Top80PercentFilter.FilterStatistics stats = realFilter.calculateFilterStatistics(
            testData, filteredData, Top80PercentFilter.DebtDetailDTO::getAmount);
        
        assertEquals(testData.size(), stats.getOriginalCount(), "原始记录数应正确");
        assertEquals(filteredData.size(), stats.getFilteredCount(), "筛选后记录数应正确");
        assertTrue(stats.getFilteredCountRatio() >= 0 && stats.getFilteredCountRatio() <= 100, 
                  "筛选比例应在0-100%之间");

        logger.info("前80%筛选算法测试通过，原始数据{}条，筛选后{}条", testData.size(), filteredData.size());
    }

    /**
     * 测试算法边界条件
     */
    @Test
    void testAlgorithmBoundaryConditions() {
        logger.info("测试算法边界条件");

        DebtDisposalSplitter realSplitter = new DebtDisposalSplitter();
        Top80PercentFilter realFilter = new Top80PercentFilter();

        // 测试拆分算法的边界条件
        // 1. 处置金额为0
        DebtDisposalSplitter.DebtDisposalResult zeroResult = realSplitter.splitDisposal(
            BigDecimal.ZERO, new BigDecimal("1000000"));
        assertEquals(BigDecimal.ZERO, zeroResult.getNewDebtDisposal(), "处置金额为0时新增债权处置应为0");
        assertEquals(BigDecimal.ZERO, zeroResult.getStockDebtDisposal(), "处置金额为0时存量债权处置应为0");

        // 2. 新增债权为0
        DebtDisposalSplitter.DebtDisposalResult noNewDebtResult = realSplitter.splitDisposal(
            new BigDecimal("1000000"), BigDecimal.ZERO);
        assertEquals(BigDecimal.ZERO, noNewDebtResult.getNewDebtDisposal(), "新增债权为0时新增债权处置应为0");
        assertEquals(new BigDecimal("1000000"), noNewDebtResult.getStockDebtDisposal(), "新增债权为0时全部归存量处置");

        // 测试筛选算法的边界条件
        // 1. 空列表
        List<Top80PercentFilter.DebtDetailDTO> emptyResult = realFilter.filterTop80Percent(
            Arrays.asList(), Top80PercentFilter.DebtDetailDTO::getAmount, new BigDecimal("1000000"));
        assertTrue(emptyResult.isEmpty(), "空列表筛选结果应为空");

        // 2. 单个元素
        List<Top80PercentFilter.DebtDetailDTO> singleData = Arrays.asList(
            new Top80PercentFilter.DebtDetailDTO("公司A", "债权人1", "债务人1", new BigDecimal("2000000")));
        List<Top80PercentFilter.DebtDetailDTO> singleResult = realFilter.filterTop80Percent(
            singleData, Top80PercentFilter.DebtDetailDTO::getAmount, new BigDecimal("1000000"));
        assertEquals(1, singleResult.size(), "单个符合条件的元素应被保留");

        logger.info("算法边界条件测试通过");
    }
}