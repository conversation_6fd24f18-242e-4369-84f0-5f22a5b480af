package com.laoshu198838.util.business;

import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 债权处置金额拆分工具类
 * 实现处置金额在新增债权和存量债权之间的拆分逻辑
 * 
 * 核心规则：
 * 1. 新增债权处置不能超过当年新增金额
 * 2. 超出部分归为存量债权处置
 * 3. 确保拆分后的总金额等于原始处置金额
 * 
 * <AUTHOR> Code
 * @since 2025-01-18
 */
@Component
public class DebtDisposalSplitter {

    /**
     * 拆分处置金额到新增债权和存量债权
     * 
     * @param disposalAmount 总处置金额
     * @param newDebtAmount 当年新增债权金额
     * @return 拆分结果
     */
    public DebtDisposalResult splitDisposal(BigDecimal disposalAmount, BigDecimal newDebtAmount) {
        // 参数验证
        if (disposalAmount == null || disposalAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("处置金额不能为空或负数");
        }
        if (newDebtAmount == null || newDebtAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("新增债权金额不能为空或负数");
        }
        
        DebtDisposalResult result = new DebtDisposalResult();
        
        if (disposalAmount.compareTo(newDebtAmount) <= 0) {
            // 情况1: 处置金额 <= 新增金额，全部归为新增债权处置
            result.setNewDebtDisposal(disposalAmount);
            result.setStockDebtDisposal(BigDecimal.ZERO);
            result.setSplitReason("处置金额未超出新增金额");
        } else {
            // 情况2: 处置金额 > 新增金额，需要拆分
            result.setNewDebtDisposal(newDebtAmount);
            result.setStockDebtDisposal(disposalAmount.subtract(newDebtAmount));
            result.setSplitReason("处置金额超出新增金额，超出部分归为存量处置");
        }
        
        // 设置原始数据
        result.setOriginalDisposal(disposalAmount);
        result.setOriginalNewDebt(newDebtAmount);
        
        return result;
    }

    /**
     * 批量处理债权处置拆分
     * 
     * @param dataList 待拆分的数据列表
     * @param disposalExtractor 处置金额提取函数
     * @param newDebtExtractor 新增债权金额提取函数
     * @return 拆分结果列表
     */
    public <T> List<DebtDisposalResultWithData<T>> batchSplitDisposal(
            List<T> dataList,
            Function<T, BigDecimal> disposalExtractor,
            Function<T, BigDecimal> newDebtExtractor) {
        
        return dataList.stream()
            .map(data -> {
                BigDecimal disposalAmount = disposalExtractor.apply(data);
                BigDecimal newDebtAmount = newDebtExtractor.apply(data);
                DebtDisposalResult splitResult = splitDisposal(disposalAmount, newDebtAmount);
                return new DebtDisposalResultWithData<>(data, splitResult);
            })
            .collect(Collectors.toList());
    }

    /**
     * 验证拆分结果的正确性
     * 
     * @param result 拆分结果
     * @return 是否有效
     */
    public boolean validateSplitResult(DebtDisposalResult result) {
        if (result == null) {
            return false;
        }
        
        // 验证拆分后的总金额等于原始金额
        BigDecimal total = result.getNewDebtDisposal().add(result.getStockDebtDisposal());
        return total.compareTo(result.getOriginalDisposal()) == 0;
    }

    /**
     * 计算拆分统计信息
     * 
     * @param results 拆分结果列表
     * @return 统计信息
     */
    public DisposalSplitStatistics calculateStatistics(List<DebtDisposalResult> results) {
        if (results == null || results.isEmpty()) {
            return new DisposalSplitStatistics();
        }

        DisposalSplitStatistics stats = new DisposalSplitStatistics();
        
        BigDecimal totalOriginal = BigDecimal.ZERO;
        BigDecimal totalNewDebtDisposal = BigDecimal.ZERO;
        BigDecimal totalStockDebtDisposal = BigDecimal.ZERO;
        int splitCount = 0;
        int noSplitCount = 0;

        for (DebtDisposalResult result : results) {
            totalOriginal = totalOriginal.add(result.getOriginalDisposal());
            totalNewDebtDisposal = totalNewDebtDisposal.add(result.getNewDebtDisposal());
            totalStockDebtDisposal = totalStockDebtDisposal.add(result.getStockDebtDisposal());
            
            if (result.getStockDebtDisposal().compareTo(BigDecimal.ZERO) > 0) {
                splitCount++;
            } else {
                noSplitCount++;
            }
        }

        stats.setTotalCount(results.size());
        stats.setSplitCount(splitCount);
        stats.setNoSplitCount(noSplitCount);
        stats.setTotalOriginalDisposal(totalOriginal);
        stats.setTotalNewDebtDisposal(totalNewDebtDisposal);
        stats.setTotalStockDebtDisposal(totalStockDebtDisposal);
        stats.setSplitRatio(splitCount * 100.0 / results.size());

        return stats;
    }

    /**
     * 处置金额拆分结果
     */
    public static class DebtDisposalResult {
        private BigDecimal originalDisposal;    // 原始处置金额
        private BigDecimal originalNewDebt;     // 原始新增金额
        private BigDecimal newDebtDisposal;     // 新增债权处置金额
        private BigDecimal stockDebtDisposal;   // 存量债权处置金额
        private String splitReason;             // 拆分原因说明

        public DebtDisposalResult() {
            this.originalDisposal = BigDecimal.ZERO;
            this.originalNewDebt = BigDecimal.ZERO;
            this.newDebtDisposal = BigDecimal.ZERO;
            this.stockDebtDisposal = BigDecimal.ZERO;
            this.splitReason = "";
        }

        /**
         * 验证拆分结果的正确性
         */
        public boolean isValid() {
            BigDecimal total = newDebtDisposal.add(stockDebtDisposal);
            return total.compareTo(originalDisposal) == 0;
        }

        // Getters and Setters
        public BigDecimal getOriginalDisposal() { return originalDisposal; }
        public void setOriginalDisposal(BigDecimal originalDisposal) { this.originalDisposal = originalDisposal; }

        public BigDecimal getOriginalNewDebt() { return originalNewDebt; }
        public void setOriginalNewDebt(BigDecimal originalNewDebt) { this.originalNewDebt = originalNewDebt; }

        public BigDecimal getNewDebtDisposal() { return newDebtDisposal; }
        public void setNewDebtDisposal(BigDecimal newDebtDisposal) { this.newDebtDisposal = newDebtDisposal; }

        public BigDecimal getStockDebtDisposal() { return stockDebtDisposal; }
        public void setStockDebtDisposal(BigDecimal stockDebtDisposal) { this.stockDebtDisposal = stockDebtDisposal; }

        public String getSplitReason() { return splitReason; }
        public void setSplitReason(String splitReason) { this.splitReason = splitReason; }

        @Override
        public String toString() {
            return String.format("DebtDisposalResult{原始处置=%.2f, 新增金额=%.2f, 新增处置=%.2f, 存量处置=%.2f, 原因='%s'}",
                    originalDisposal.doubleValue(), originalNewDebt.doubleValue(),
                    newDebtDisposal.doubleValue(), stockDebtDisposal.doubleValue(), splitReason);
        }
    }

    /**
     * 带原始数据的拆分结果
     */
    public static class DebtDisposalResultWithData<T> {
        private T originalData;
        private DebtDisposalResult splitResult;

        public DebtDisposalResultWithData(T originalData, DebtDisposalResult splitResult) {
            this.originalData = originalData;
            this.splitResult = splitResult;
        }

        public T getOriginalData() { return originalData; }
        public void setOriginalData(T originalData) { this.originalData = originalData; }

        public DebtDisposalResult getSplitResult() { return splitResult; }
        public void setSplitResult(DebtDisposalResult splitResult) { this.splitResult = splitResult; }
    }

    /**
     * 拆分统计信息
     */
    public static class DisposalSplitStatistics {
        private int totalCount = 0;                     // 总记录数
        private int splitCount = 0;                     // 需要拆分的记录数
        private int noSplitCount = 0;                   // 不需要拆分的记录数
        private double splitRatio = 0.0;               // 拆分比例(%)
        private BigDecimal totalOriginalDisposal = BigDecimal.ZERO;    // 原始处置金额总计
        private BigDecimal totalNewDebtDisposal = BigDecimal.ZERO;     // 新增债权处置金额总计
        private BigDecimal totalStockDebtDisposal = BigDecimal.ZERO;   // 存量债权处置金额总计

        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }

        public int getSplitCount() { return splitCount; }
        public void setSplitCount(int splitCount) { this.splitCount = splitCount; }

        public int getNoSplitCount() { return noSplitCount; }
        public void setNoSplitCount(int noSplitCount) { this.noSplitCount = noSplitCount; }

        public double getSplitRatio() { return splitRatio; }
        public void setSplitRatio(double splitRatio) { this.splitRatio = splitRatio; }

        public BigDecimal getTotalOriginalDisposal() { return totalOriginalDisposal; }
        public void setTotalOriginalDisposal(BigDecimal totalOriginalDisposal) { this.totalOriginalDisposal = totalOriginalDisposal; }

        public BigDecimal getTotalNewDebtDisposal() { return totalNewDebtDisposal; }
        public void setTotalNewDebtDisposal(BigDecimal totalNewDebtDisposal) { this.totalNewDebtDisposal = totalNewDebtDisposal; }

        public BigDecimal getTotalStockDebtDisposal() { return totalStockDebtDisposal; }
        public void setTotalStockDebtDisposal(BigDecimal totalStockDebtDisposal) { this.totalStockDebtDisposal = totalStockDebtDisposal; }

        @Override
        public String toString() {
            return String.format("DisposalSplitStatistics{总记录=%d, 拆分记录=%d, 拆分比例=%.1f%%, 总处置金额=%.2f万}",
                    totalCount, splitCount, splitRatio, totalOriginalDisposal.divide(new BigDecimal("10000")).doubleValue());
        }
    }
}